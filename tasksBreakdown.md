# **Batch Training Roster System — Task List**

---

## **I. Prerequisite Data & Schema Setup**

### 1. **Schema & Migrations**
- [x] Add/Update models: `Location`, `Trainer`, `TrainerSkill`, `Module`, `TrainerModule`, `ScheduleTemplate`, `Batch`, `BatchModule`, `Holiday`
- [x] Remove room and batch-specific holiday logic
- [x] Migrate database and push changes

### 2. **Seed/Master Data**
- [ ] Create locations (e.g., BLR, Hyd, Mum)
- [ ] Create modules (with name, description, duration)
- [ ] Create trainers (with user info)
- [ ] Map trainer skills
- [ ] Map eligible modules to trainers (`TrainerModule`)
- [ ] Create schedule templates for each batch type (30-day plan)
- [ ] Set up holidays (global and location-specific)
- [ ] Set up trainer availability for the relevant period

---

## **II. Admin/Master Data Management UI**

### 3. **Location Management**
- [ ] List/add/edit/delete locations

### 4. **Module Management**
- [ ] List/add/edit/delete modules

### 5. **Trainer Management**
- [ ] List/add/edit/delete trainers
- [ ] Assign skills to trainers
- [ ] Assign eligible modules to trainers (`TrainerModule`)
- [ ] Set trainer availability (calendar or table)

### 6. **Schedule Template Management**
- [ ] For each batch type, define/edit the 30-day module sequence (drag-and-drop or table input)

### 7. **Holiday Management**
- [ ] List/add/edit/delete holidays
- [ ] Specify if holiday is global or location-specific

---

## **III. Batch & Roster Management**

### 8. **Batch Creation**
- [ ] UI to create batches (location, batch type, count, date of joining)
- [ ] Option to add batch-specific remarks or notes

### 9. **Roster Generation**
- [ ] Logic to generate 30-day schedule for each batch using the schedule template
- [ ] For each module/day:
    - Skip holidays and Sundays
    - Mark OJT for Saturdays
    - Assign trainer (from eligible trainers, based on availability and fair distribution)
    - Assign location (already set)
    - Create `BatchModule` records

### 10. **Manual Adjustments**
- [ ] UI to override auto-assignments (change trainer/module for a day)

---

## **IV. Views & Reporting**

### 11. **Roster Views**
- [ ] **Trainer Calendar View**: See each trainer’s daily assignments (module, batch, location, holiday, OJT, Sunday, free)
- [ ] **Batch Calendar View**: See each batch’s daily schedule (module, trainer, location, holiday, OJT, Sunday)
- [ ] **Location Holiday Calendar**: See holidays for each location in a calendar view

### 12. **Export/Reporting**
- [ ] Export roster to Excel/PDF
- [ ] Download/print views

---

## **V. Optional/Advanced**

### 13. **Notifications**
- [ ] Notify trainers of their assignments

### 14. **Conflict Detection**
- [ ] Warn if no eligible/available trainer for a module/day

### 15. **Analytics**
- [ ] Trainer utilization reports
- [ ] Module delivery stats

---

# **Summary Table of Tasks**

| Area                | Tasks                                                                 |
|---------------------|-----------------------------------------------------------------------|
| Schema/Data         | Models, migrations, seed data                                         |
| Admin UI            | Locations, modules, trainers, skills, trainer-module, templates, holidays |
| Batch/Roster Mgmt   | Batch creation, roster generation, manual adjustment                  |
| Views/Reporting     | Trainer/batch/location calendars, export                              |
| Optional            | Notifications, conflict detection, analytics                          |

---

**Let me know if you want this broken down into even more granular subtasks, or if you want sample wireframes, API specs, or code templates for any part!**