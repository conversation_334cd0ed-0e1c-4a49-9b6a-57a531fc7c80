-- AlterTable
ALTER TABLE `Batch` ADD COLUMN `roasterId` INTEGER NULL;

-- CreateTable
CREATE TABLE `Roaster` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdBy` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Batch` ADD CONSTRAINT `Batch_roasterId_fkey` FOREIGN KEY (`roasterId`) REFERENCES `Roaster`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
