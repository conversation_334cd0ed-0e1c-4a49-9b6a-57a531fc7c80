/*
  Warnings:

  - You are about to drop the column `duration` on the `Module` table. All the data in the column will be lost.
  - You are about to drop the column `batchType` on the `ScheduleTemplate` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[templateId,dayNumber,moduleId,groupType]` on the table `ScheduleTemplate` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `endTime` to the `BatchModule` table without a default value. This is not possible if the table is not empty.
  - Added the required column `startTime` to the `BatchModule` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `BatchModule` table without a default value. This is not possible if the table is not empty.
  - Added the required column `duration` to the `ScheduleTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `endTime` to the `ScheduleTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `startTime` to the `ScheduleTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `templateId` to the `ScheduleTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `ScheduleTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `BatchModule` ADD COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    ADD COLUMN `createdBy` INTEGER NULL,
    ADD COLUMN `deletedAt` DATETIME(3) NULL,
    ADD COLUMN `deletedBy` INTEGER NULL,
    ADD COLUMN `endTime` DATETIME(3) NOT NULL,
    ADD COLUMN `startTime` DATETIME(3) NOT NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NOT NULL,
    ADD COLUMN `updatedBy` INTEGER NULL;

-- AlterTable
ALTER TABLE `Module` DROP COLUMN `duration`;

-- AlterTable
ALTER TABLE `Roaster` ADD COLUMN `templateId` INTEGER NULL;

-- AlterTable
ALTER TABLE `ScheduleTemplate` DROP COLUMN `batchType`,
    ADD COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    ADD COLUMN `createdBy` INTEGER NULL,
    ADD COLUMN `deletedAt` DATETIME(3) NULL,
    ADD COLUMN `deletedBy` INTEGER NULL,
    ADD COLUMN `duration` INTEGER NOT NULL,
    ADD COLUMN `endTime` VARCHAR(191) NOT NULL,
    ADD COLUMN `startTime` VARCHAR(191) NOT NULL,
    ADD COLUMN `templateId` INTEGER NOT NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NOT NULL,
    ADD COLUMN `updatedBy` INTEGER NULL;

-- CreateTable
CREATE TABLE `Template` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `batchType` ENUM('SA', 'SM', 'OPTUM', 'SA_SM') NOT NULL,
    `version` INTEGER NOT NULL DEFAULT 1,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdBy` INTEGER NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `updatedBy` INTEGER NULL,
    `deletedAt` DATETIME(3) NULL,
    `deletedBy` INTEGER NULL,

    INDEX `Template_batchType_isActive_idx`(`batchType`, `isActive`),
    UNIQUE INDEX `Template_name_batchType_version_key`(`name`, `batchType`, `version`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LocationWorkingHours` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `locationId` INTEGER NOT NULL,
    `startTime` VARCHAR(191) NOT NULL,
    `endTime` VARCHAR(191) NOT NULL,
    `lunchStartTime` VARCHAR(191) NOT NULL,
    `lunchEndTime` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `createdBy` INTEGER NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `updatedBy` INTEGER NULL,
    `deletedAt` DATETIME(3) NULL,
    `deletedBy` INTEGER NULL,

    UNIQUE INDEX `LocationWorkingHours_locationId_key`(`locationId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `BatchModule_batchId_date_idx` ON `BatchModule`(`batchId`, `date`);

-- CreateIndex
CREATE UNIQUE INDEX `ScheduleTemplate_templateId_dayNumber_moduleId_groupType_key` ON `ScheduleTemplate`(`templateId`, `dayNumber`, `moduleId`, `groupType`);

-- AddForeignKey
ALTER TABLE `ScheduleTemplate` ADD CONSTRAINT `ScheduleTemplate_templateId_fkey` FOREIGN KEY (`templateId`) REFERENCES `Template`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LocationWorkingHours` ADD CONSTRAINT `LocationWorkingHours_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Roaster` ADD CONSTRAINT `Roaster_templateId_fkey` FOREIGN KEY (`templateId`) REFERENCES `Template`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
