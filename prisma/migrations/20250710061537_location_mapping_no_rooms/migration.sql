/*
  Warnings:

  - You are about to drop the column `roomId` on the `Batch` table. All the data in the column will be lost.
  - You are about to drop the `Room` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RoomAvailability` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `locationId` to the `Batch` table without a default value. This is not possible if the table is not empty.
  - Added the required column `locationId` to the `BatchModule` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `Batch` DROP FOREIGN KEY `Batch_roomId_fkey`;

-- DropForeignKey
ALTER TABLE `RoomAvailability` DROP FOREIGN KEY `RoomAvailability_roomId_fkey`;

-- AlterTable
ALTER TABLE `Batch` DROP COLUMN `roomId`,
    ADD COLUMN `locationId` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `BatchModule` ADD COLUMN `locationId` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `Holiday` ADD COLUMN `locationId` INTEGER NULL;

-- DropTable
DROP TABLE `Room`;

-- DropTable
DROP TABLE `RoomAvailability`;

-- CreateTable
CREATE TABLE `Location` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `Location_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `BatchModule` ADD CONSTRAINT `BatchModule_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Holiday` ADD CONSTRAINT `Holiday_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Batch` ADD CONSTRAINT `Batch_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
