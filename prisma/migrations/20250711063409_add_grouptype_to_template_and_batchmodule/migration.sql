/*
  Warnings:

  - You are about to alter the column `batchType` on the `ScheduleTemplate` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Enum(EnumId(5))`.
  - Added the required column `groupType` to the `BatchModule` table without a default value. This is not possible if the table is not empty.
  - Added the required column `groupType` to the `ScheduleTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `BatchModule` ADD COLUMN `groupType` ENUM('SC', 'SM', 'ALL') NOT NULL;

-- AlterTable
ALTER TABLE `ScheduleTemplate` ADD COLUMN `groupType` ENUM('SC', 'SM', 'ALL') NOT NULL,
    MODIFY `batchType` ENUM('SC', 'SM', 'OPTUM', 'SC_SM') NOT NULL;
