/*
  Warnings:

  - Added the required column `franchiseType` to the `Store` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `Candidate` ADD COLUMN `franchiseType` ENUM('COCO', 'FOFO') NULL,
    ADD COLUMN `recruiterName` VARCHAR(191) NULL,
    ADD COLUMN `recruiterPhone` VARCHAR(191) NULL,
    ADD COLUMN `remarks` VARCHAR(191) NULL,
    ADD COLUMN `reportingAOM` VARCHAR(191) NULL,
    ADD COLUMN `storeCityHiredFor` VARCHAR(191) NULL,
    ADD COLUMN `storeCodeHiredFor` VARCHAR(191) NULL,
    ADD COLUMN `storeStateHiredFor` VARCHAR(191) NULL,
    ADD COLUMN `travelRequired` BOOLEAN NULL,
    ADD COLUMN `turboLink` VARCHAR(191) NULL,
    ADD COLUMN `zm` VARCHAR(191) NULL;

-- Add the enum column as nullable first
ALTER TABLE `Store` ADD COLUMN `franchiseType` ENUM('COCO', 'FOFO') NULL,
    ADD COLUMN `state` VARCHAR(191) NULL;

-- Update existing rows to have a default value
UPDATE `Store` SET `franchiseType` = 'COCO' WHERE `franchiseType` IS NULL;

-- Alter the column to be NOT NULL
ALTER TABLE `Store` MODIFY COLUMN `franchiseType` ENUM('COCO', 'FOFO') NOT NULL;

-- AlterTable
ALTER TABLE `TravelInfo` ADD COLUMN `amount` DOUBLE NULL,
    ADD COLUMN `bookingStatus` VARCHAR(191) NULL,
    ADD COLUMN `dateOfBooking` DATETIME(3) NULL,
    ADD COLUMN `fromLocation` VARCHAR(191) NULL,
    ADD COLUMN `modeOfTravel` VARCHAR(191) NULL,
    ADD COLUMN `pnr` VARCHAR(191) NULL,
    ADD COLUMN `quota` VARCHAR(191) NULL,
    ADD COLUMN `remarks` VARCHAR(191) NULL,
    ADD COLUMN `ticketStatus` VARCHAR(191) NULL,
    ADD COLUMN `toLocation` VARCHAR(191) NULL,
    ADD COLUMN `travelFrom` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `age` INTEGER NULL,
    ADD COLUMN `designation` VARCHAR(191) NULL,
    ADD COLUMN `gender` VARCHAR(191) NULL,
    ADD COLUMN `homeCity` VARCHAR(191) NULL;
