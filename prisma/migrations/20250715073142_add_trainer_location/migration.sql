/*
  Warnings:

  - A unique constraint covering the columns `[batchType,groupType,dayNumber]` on the table `ScheduleTemplate` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[trainerId,availableDate]` on the table `TrainerAvailability` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `locationId` to the `Trainer` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `Trainer` ADD COLUMN `locationId` INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `ScheduleTemplate_batchType_groupType_dayNumber_key` ON `ScheduleTemplate`(`batchType`, `groupType`, `dayNumber`);


-- AddForeignKey
ALTER TABLE `Trainer` ADD CONSTRAINT `Trainer_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
