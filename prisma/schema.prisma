// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum FranchiseType {
  COCO
  FOFO
}

model Candidate {
  id           Int    @id @default(autoincrement())
  userId       Int?   @unique
  user         User?  @relation(fields: [userId], references: [id])
  jobType      String
  personNumber String @unique
  joiningDate  DateTime
  joiningDay   String
  status       String
  onboarding   OnboardingInfo?
  assessments  Assessment[]
  attendance   Attendance[]
  travelInfo   TravelInfo?
  batches      Batch[] @relation("BatchCandidates")
  recruiterName     String?
  recruiterPhone    String?
  travelRequired    Boolean?
  turboLink         String?
  remarks           String?
  reportingAOM      String?
  zm                String?
  storeCodeHiredFor String?
  storeCityHiredFor String?
  storeStateHiredFor String?
  franchiseType     FranchiseType?
}

model OnboardingInfo {
  id          Int   @id @default(autoincrement())
  candidate   Candidate @relation(fields: [candidateId], references: [id])
  candidateId Int   @unique
  employeeId  String
  aadharName  String
  createdAt   DateTime @default(now())
  createdBy   Int?
  updatedAt   DateTime @updatedAt
  updatedBy   Int?
  deletedAt   DateTime?
  deletedBy   Int?
}

model Assessment {
  id          Int   @id @default(autoincrement())
  candidate   Candidate @relation(fields: [candidateId], references: [id])
  candidateId Int
  type        String
  date        DateTime
  score       Int
  result      String
  evaluator   Trainer  @relation("TrainerAssessments", fields: [evaluatorId], references: [id])
  evaluatorId Int
  remarks     String?
  createdAt   DateTime @default(now())
  createdBy   Int?
  updatedAt   DateTime @updatedAt
  updatedBy   Int?
  deletedAt   DateTime?
  deletedBy   Int?
}

model Trainer {
  id                Int    @id @default(autoincrement())
  userId            Int    @unique
  user              User   @relation(fields: [userId], references: [id])
  isLeadFacilitator Boolean
  locationId        Int
  location          Location @relation(fields: [locationId], references: [id])
  availability      TrainerAvailability[]
  assessments       Assessment[] @relation("TrainerAssessments")
  skills            TrainerSkill[]
  batchModules      BatchModule[]
  trainerModules    TrainerModule[]
}

model Skill {
  id      Int       @id @default(autoincrement())
  name    String    @unique
  trainers TrainerSkill[]
}

model TrainerSkill {
  id        Int     @id @default(autoincrement())
  trainer   Trainer @relation(fields: [trainerId], references: [id])
  trainerId Int
  skill     Skill   @relation(fields: [skillId], references: [id])
  skillId   Int

  @@unique([trainerId, skillId])
}

model TrainerAvailability {
  id            Int   @id @default(autoincrement())
  trainer       Trainer  @relation(fields: [trainerId], references: [id])
  trainerId     Int
  availableDate DateTime
  isAvailable   Boolean
  createdAt     DateTime @default(now())
  createdBy     Int?
  updatedAt     DateTime @updatedAt
  updatedBy     Int?
  deletedAt     DateTime?
  deletedBy     Int?

  @@unique([trainerId, availableDate])
}

model Store {
  id                 Int   @id @default(autoincrement())
  storeCode          String   @unique
  region             String
  storeName          String
  isOjtEnabled       Boolean
  currentLearnerCount Int
  state         String?
  franchiseType FranchiseType
  createdAt          DateTime @default(now())
  createdBy          Int?
  updatedAt          DateTime @updatedAt
  updatedBy          Int?
  deletedAt          DateTime?
  deletedBy          Int?
}

enum ModuleNature {
  CRT
  OJT
}

enum ProgramType {
  SA
  SM
  OPTOM
}

model Module {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  duration    Int      // in days
  nature      ModuleNature // CRT or OJT
  program     ProgramType  // SA, SM, or OPTOM
  scheduleTemplates ScheduleTemplate[]
  batchModules BatchModule[]
  trainerModules TrainerModule[]
}

model BatchModule {
  id         Int      @id @default(autoincrement())
  batch      Batch    @relation(fields: [batchId], references: [id])
  batchId    Int
  module     Module   @relation(fields: [moduleId], references: [id])
  moduleId   Int
  trainer    Trainer  @relation(fields: [trainerId], references: [id])
  trainerId  Int
  location   Location @relation(fields: [locationId], references: [id])
  locationId Int
  date       DateTime
  startTime  DateTime // Start time of the module session
  endTime    DateTime // End time of the module session
  groupType  GroupType
  createdAt  DateTime @default(now())
  createdBy  Int?
  updatedAt  DateTime @updatedAt
  updatedBy  Int?
  deletedAt  DateTime?
  deletedBy  Int?

  @@index([batchId, date]) // Index for efficient querying of modules by batch and date
}

model Holiday {
  id         Int       @id @default(autoincrement())
  date       DateTime
  reason     String?
  location   Location? @relation(fields: [locationId], references: [id])
  locationId Int?
}

model Template {
  id              Int               @id @default(autoincrement())
  name            String
  description     String?
  batchType       BatchType
  version         Int               @default(1)
  isActive        Boolean           @default(true)
  scheduleTemplates ScheduleTemplate[]
  roasters        Roaster[]
  createdAt       DateTime          @default(now())
  createdBy       Int?
  updatedAt       DateTime          @updatedAt
  updatedBy       Int?
  deletedAt       DateTime?
  deletedBy       Int?

  @@unique([name, batchType, version])
  @@index([batchType, isActive])
}

model ScheduleTemplate {
  id           Int        @id @default(autoincrement())
  template     Template   @relation(fields: [templateId], references: [id])
  templateId   Int
  dayNumber    Int
  module       Module     @relation(fields: [moduleId], references: [id])
  moduleId     Int
  groupType    GroupType
  startTime    String     // Time in HH:MM format (e.g., "09:00", "14:00")
  endTime      String     // Time in HH:MM format (e.g., "12:00", "17:00")
  duration     Int        // Duration in hours (e.g., 3 for 3-hour session)
  createdAt    DateTime   @default(now())
  createdBy    Int?
  updatedAt    DateTime   @updatedAt
  updatedBy    Int?
  deletedAt    DateTime?
  deletedBy    Int?

  @@unique([templateId, dayNumber, moduleId, groupType])
}

enum BatchType {
  SA
  SM
  OPTUM
  SA_SM
}

model Batch {
  id          Int   @id @default(autoincrement())
  batchName   String
  batchType   BatchType
  batchDate   DateTime
  location    Location @relation(fields: [locationId], references: [id])
  locationId  Int
  candidates  Candidate[] @relation("BatchCandidates")
  isLabBased  Boolean
  remarks     String?
  attendance  Attendance[]
  batchModules BatchModule[]
  createdAt   DateTime @default(now())
  createdBy   Int?
  updatedAt   DateTime @updatedAt
  updatedBy   Int?
  deletedAt   DateTime?
  deletedBy   Int?
  roaster     Roaster? @relation(fields: [roasterId], references: [id])
  roasterId   Int?
}

model Attendance {
  id         Int   @id @default(autoincrement())
  candidate  Candidate @relation(fields: [candidateId], references: [id])
  candidateId Int
  batch      Batch     @relation(fields: [batchId], references: [id])
  batchId    Int
  date       DateTime
  status     String
  photoUrl   String?
  createdAt  DateTime @default(now())
  createdBy  Int?
  updatedAt  DateTime @updatedAt
  updatedBy  Int?
  deletedAt  DateTime?
  deletedBy  Int?
}

model TravelInfo {
  id           Int   @id @default(autoincrement())
  candidate    Candidate @relation(fields: [candidateId], references: [id])
  candidateId  Int   @unique
  travelStatus String
  destination  String
  ticketNumber String
  travelDate   DateTime
  modeOfTravel      String?
  travelFrom        String?
  bookingStatus     String?
  dateOfBooking     DateTime?
  fromLocation      String?
  toLocation        String?
  quota             String?
  pnr               String?
  amount            Float?
  ticketStatus      String?
  remarks           String?
  createdAt    DateTime @default(now())
  createdBy    Int?
  updatedAt    DateTime @updatedAt
  updatedBy    Int?
  deletedAt    DateTime?
  deletedBy    Int?
}

model User {
  id           Int      @id @default(autoincrement())
  name         String
  email        String   @unique
  passwordHash String
  phoneNumber  String   @unique
  isActive     Boolean  @default(true)
  gender       String?
  age          Int?
  designation  String?
  homeCity     String?
  createdAt    DateTime @default(now())
  createdBy    Int?
  updatedAt    DateTime @updatedAt
  updatedBy    Int?
  deletedAt    DateTime?
  deletedBy    Int?
  candidate    Candidate?
  trainer      Trainer?
  userRoles    UserRole[]
}

model Role {
  id    Int   @id @default(autoincrement())
  name  String   @unique
  userRoles UserRole[]
  rolePermissions RolePermission[]
  createdAt  DateTime @default(now())
  createdBy  Int?
  updatedAt  DateTime @updatedAt
  updatedBy  Int?
  deletedAt  DateTime?
  deletedBy  Int?
}

model Permission {
  id    Int   @id @default(autoincrement())
  name  String   @unique
  rolePermissions RolePermission[]
  createdAt  DateTime @default(now())
  createdBy  Int?
  updatedAt  DateTime @updatedAt
  updatedBy  Int?
  deletedAt  DateTime?
  deletedBy  Int?
}

model UserRole {
  id     Int @id @default(autoincrement())
  user   User   @relation(fields: [userId], references: [id])
  userId Int
  role   Role   @relation(fields: [roleId], references: [id])
  roleId Int
  @@unique([userId, roleId])
  createdAt  DateTime @default(now())
  createdBy  Int?
  updatedAt  DateTime @updatedAt
  updatedBy  Int?
  deletedAt  DateTime?
  deletedBy  Int?
}

model RolePermission {
  id           Int @id @default(autoincrement())
  role         Role   @relation(fields: [roleId], references: [id])
  roleId       Int
  permission   Permission @relation(fields: [permissionId], references: [id])
  permissionId Int
  @@unique([roleId, permissionId])
  createdAt  DateTime @default(now())
  createdBy  Int?
  updatedAt  DateTime @updatedAt
  updatedBy  Int?
  deletedAt  DateTime?
  deletedBy  Int?
}

model AuditLog {
  id         String   @id @default(uuid())
  action     String
  tableName  String
  recordId   String
  userId     Int?     // nullable for system actions
  timestamp  DateTime @default(now())
  oldValue   Json?
  newValue   Json?
  metadata   Json?
}

model Location {
  id      Int    @id @default(autoincrement())
  name    String @unique // e.g., "BLR", "Hyd", "Mum"
  circle  String         // e.g., "South", "West", etc. (region)
  address String
  type    LocationType
  batches Batch[]
  batchModules BatchModule[]
  holidays Holiday[]
  trainers  Trainer[] 
  workingHours LocationWorkingHours?
}

model LocationWorkingHours {
  id              Int      @id @default(autoincrement())
  location        Location @relation(fields: [locationId], references: [id])
  locationId      Int      @unique
  startTime       String   // Working start time (e.g., "09:00")
  endTime         String   // Working end time (e.g., "18:00")
  lunchStartTime  String   // Lunch start time (e.g., "13:00")
  lunchEndTime    String   // Lunch end time (e.g., "14:00")
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  createdBy       Int?
  updatedAt       DateTime @updatedAt
  updatedBy       Int?
  deletedAt       DateTime?
  deletedBy       Int?
}

model TrainerModule {
  id         Int     @id @default(autoincrement())
  trainer    Trainer @relation(fields: [trainerId], references: [id])
  trainerId  Int
  module     Module  @relation(fields: [moduleId], references: [id])
  moduleId   Int

  @@unique([trainerId, moduleId])
}

enum GroupType {
  SA
  SM
  ALL
}

model Roaster {
  id        Int      @id @default(autoincrement())
  name      String
  createdAt DateTime @default(now())
  createdBy Int?
  batches   Batch[]
  template  Template? @relation(fields: [templateId], references: [id])
  templateId Int?
}

enum LocationType {
  ACADEMY
  SATELLITE
}
