# Batch Scheduling & Trainer Assignment: Data Flow and Confirmation Table Generation

---

## 1. Data Fetching: Tables and Fields

The backend fetches data from the following tables for batch scheduling and trainer assignment:

- **ScheduleTemplate**: (Fields: batchType, groupType, dayNumber, moduleId)
  - Determines which module(s) each group does on each day.
- **Module**: (Fields: id, name, duration)
  - Module details.
- **Holiday**: (Fields: date, locationId)
  - Used to skip/mark holidays in the schedule.
- **Trainer**: (Fields: id, userId, ...)
  - All trainers.
- **TrainerModule**: (Fields: trainerId, moduleId)
  - Mapping of trainers to modules they can teach.
- **TrainerAvailability**: (Fields: trainerId, availableDate, isAvailable)
  - Dates when trainers are available.
- **Batch**: (Fields: id, batchName, batchType, locationId, ...)
  - For batch creation and mapping.
- **Location**: (Fields: id, name)
  - For filtering holidays and trainers.

---

## 2. Backend Logic: Constructing the Assignments Array

### Inputs
- `batchType`, `batchCount`, `startDay`, `location`

### Steps
1. **Fetch schedule template** for the batchType (and groupType if applicable).
2. **Generate schedule**:
   - For each day, for each groupType (if any), determine the module (skip if holiday).
3. **For each batch** (usually one per groupType, or just one for non-group batches):
   - For each day, for each groupType (if any):
     - **Trainer Availability Check for Multi-day Modules:**
       - For modules that span multiple days (see `Module.duration`), calculate the full date range for the module.
       - For each candidate trainer:
         - Check that the trainer is qualified for the module (`TrainerModule`).
         - Check that the trainer is available (`TrainerAvailability.isAvailable = true`) for **all days** in the module's date range.
         - **Prevent Double-Booking:**
           - Maintain a temporary in-memory map of trainer assignments (e.g., `{ "Raj|2024-08-05": true }`).
           - Before assigning a trainer, check that they are not already assigned to another batch/module on any of the module's days.
           - Only trainers not present in the map for any of the module's days are eligible.
           - After assigning a trainer, update the map for all days the module spans.
         - If no eligible trainer is found, flag for manual intervention.
     - **Trainer Utilization (Load Balancing) for Fair Assignment:**
       - To avoid overloading some trainers and underutilizing others, use historical assignment data to balance load.
       - **Fetch utilization counts** (number of assignments in the last N months) **only for currently eligible trainers**:
         - Example SQL (for eligible trainer IDs 1, 2, 5):
           ```sql
           SELECT trainerId, COUNT(*) AS assignmentCount
           FROM BatchModule
           WHERE startDate >= DATE_SUB(CURDATE(), INTERVAL 2 MONTH)
             AND trainerId IN (1, 2, 5)
           GROUP BY trainerId
           ```
       - **Sort eligible trainers** by their utilization count (ascending).
       - **Assign the trainer** with the lowest utilization.
       - **Update the utilization map** as you assign trainers in the current scheduling run.
       
       **Pseudocode for Utilization-based Assignment:**
       ```js
       // eligibleTrainers: array of trainer objects with .id and .name
       const eligibleTrainerIds = eligibleTrainers.map(t => t.id);
       // Fetch utilization counts for these trainers from DB
       const utilizationFromDB = await getTrainerUtilization(eligibleTrainerIds);
       // Build utilization map
       const trainerUtilization = {};
       utilizationFromDB.forEach(row => {
         trainerUtilization[row.trainerId] = row.assignmentCount;
       });
       // Sort eligible trainers by utilization
       eligibleTrainers.sort((a, b) => {
         return (trainerUtilization[a.id] || 0) - (trainerUtilization[b.id] || 0);
       });
       // Assign the least-utilized trainer
       const assignedTrainer = eligibleTrainers[0];
       ```
       - This ensures that trainer assignments are distributed more evenly over time, improving fairness and utilization.
     - Assign a trainer.
     - Add an entry to the assignments array:
       ```json
       {
         "batch": "Batch 1",
         "groupType": "SA",
         "module": "OJT",
         "trainer": "Raj",
         "day": 1,
         "date": "2024-08-05"
       }
       ```
     - For non-group batches, omit `groupType`.

#### **Pseudocode for Trainer Availability and Double-Booking Check**
```js
const trainerAssignments = {}; // e.g., { "Raj|2024-08-05": true }

for (const moduleAssignment of moduleAssignments) {
  const moduleDuration = moduleAssignment.module.duration;
  const moduleDates = getDates(moduleAssignment.startDate, moduleDuration);

  const eligibleTrainers = trainers.filter(trainer => {
    // Check qualification
    if (!trainer.modules.includes(moduleAssignment.module.id)) return false;
    // Check availability and double-booking for all days
    for (const date of moduleDates) {
      const key = `${trainer.name}|${date}`;
      const isAvailable = trainer.availability.some(a => a.date === date && a.isAvailable);
      const isNotAssignedElsewhere = !trainerAssignments[key];
      if (!isAvailable || !isNotAssignedElsewhere) return false;
    }
    return true;
  });

  if (eligibleTrainers.length > 0) {
    const assignedTrainer = eligibleTrainers[0];
    // Mark trainer as assigned for all module days
    for (const date of moduleDates) {
      const key = `${assignedTrainer.name}|${date}`;
      trainerAssignments[key] = true;
    }
    assignTrainer(moduleAssignment, assignedTrainer);
  } else {
    flagForManualIntervention(moduleAssignment);
  }
}
```

### Sample Assignments Array (Group Batch Example: SA+SM)
```json
[
  {
    "batch": "Batch 1",
    "groupType": "SA",
    "module": "OJT",
    "trainer": "Raj",
    "day": 1,
    "date": "2024-08-05"
  },
  {
    "batch": "Batch 1",
    "groupType": "SM",
    "module": "Sales",
    "trainer": "Sumanth",
    "day": 1,
    "date": "2024-08-05"
  },
  {
    "batch": "Batch 1",
    "groupType": "ALL",
    "module": "Induction",
    "trainer": "Raj",
    "day": 2,
    "date": "2024-08-06"
  }
]
```

### Sample Assignments Array (Non-Group Batch Example: OPTUM)
```json
[
  {
    "batch": "Batch 1",
    "module": "Sales",
    "trainer": "Raj",
    "day": 1,
    "date": "2024-08-05"
  },
  {
    "batch": "Batch 1",
    "module": "Retail",
    "trainer": "Sumanth",
    "day": 2,
    "date": "2024-08-06"
  }
]
```

---

## 3. Frontend Logic: Generating Confirmation Tables

### 3.1 Trainer-level Table
- **Columns:** Trainers
- **Rows:** Days
- **Cell Value:** For each trainer, show which batch/module/groupType they are handling on that day

#### Example Table (SA+SM)
| Day | Date       | Raj                | Sumanth            |
|-----|------------|--------------------|--------------------|
| 1   | 2024-08-05 | Batch 1 (OJT, SA)  | Batch 1 (Sales, SM)|
| 2   | 2024-08-06 | Batch 1 (Induction, ALL) |                |

#### Pseudocode
```js
for (const day of days) {
  const row = { day, date: day.date };
  for (const trainer of trainers) {
    const assignment = assignments.find(a => a.day === day.day && a.trainer === trainer);
    if (assignment) {
      row[trainer] = assignment.groupType ? `${assignment.batch} (${assignment.module}, ${assignment.groupType})` : `${assignment.batch} (${assignment.module})`;
    } else {
      row[trainer] = '';
    }
  }
  tableRows.push(row);
}
```

### 3.2 Batch-level Table
- **Columns:** Batches
- **Rows:** Days
- **Cell Value:** For each batch, show which trainer/module/groupType is assigned on that day

#### Example Table (SA+SM)
| Day | Date       | Batch 1                |
|-----|------------|------------------------|
| 1   | 2024-08-05 | Raj (OJT, SA) <br> Sumanth (Sales, SM) |
| 2   | 2024-08-06 | Raj (Induction, ALL)   |

#### Pseudocode
```js
for (const day of days) {
  const row = { day, date: day.date };
  for (const batch of batches) {
    const assignmentsForBatch = assignments.filter(a => a.day === day.day && a.batch === batch);
    row[batch] = assignmentsForBatch.map(a => a.groupType ? `${a.trainer} (${a.module}, ${a.groupType})` : `${a.trainer} (${a.module})`).join(' \n ');
  }
  tableRows.push(row);
}
```

---

**This approach ensures both tables are generated from the same assignments array, giving users a clear view from both the trainer and batch perspectives.** 