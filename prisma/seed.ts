const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  const password = 'password123';
  const passwordHash = await bcrypt.hash(password, 10);

  // Create admin user
  const adminUser = await prisma.user.create({
    data: {
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phoneNumber: '9000000001',
      passwordHash,
      isActive: true,
      gender: 'Female',
      age: 32,
      designation: 'Admin',
      homeCity: 'Bangalore',
    },
  });

  // Create candidate users and candidates
  const candidate1 = await prisma.candidate.create({
    data: {
      jobType: 'SA',
      personNumber: 'P123456',
      joiningDate: new Date('2025-06-17'),
      joiningDay: 'Monday',
      status: 'Accepted',
      recruiterName: '<PERSON>it <PERSON>',
      recruiterPhone: '9000000010',
      travelRequired: true,
      turboLink: 'https://turbo.link/candidate1',
      remarks: 'Ready to join',
      reportingAOM: '<PERSON><PERSON>',
      zm: '<PERSON><PERSON><PERSON>',
      storeCodeHiredFor: 'BLR-KR01',
      storeCityHiredFor: 'Bangalore',
      storeStateHiredFor: 'Karnataka',
      franchiseType: 'COCO',
      user: {
        create: {
          name: 'Ravi Kumar',
          email: '<EMAIL>',
          phoneNumber: '9000000002',
          passwordHash,
          isActive: true,
          gender: 'Male',
          age: 28,
          designation: 'Sales Associate',
          homeCity: 'Bangalore',
        },
      },
    },
  });
  const candidate2 = await prisma.candidate.create({
    data: {
      jobType: 'SM',
      personNumber: 'P654321',
      joiningDate: new Date('2025-06-18'),
      joiningDay: 'Tuesday',
      status: 'Pending',
      recruiterName: 'Sunita Rao',
      recruiterPhone: '9000000011',
      travelRequired: false,
      turboLink: 'https://turbo.link/candidate2',
      remarks: 'Pending documents',
      reportingAOM: 'Ajay Mehta',
      zm: 'Suman Das',
      storeCodeHiredFor: 'MUM-AN01',
      storeCityHiredFor: 'Mumbai',
      storeStateHiredFor: 'Maharashtra',
      franchiseType: 'FOFO',
      user: {
        create: {
          name: 'Sneha Rao',
          email: '<EMAIL>',
          phoneNumber: '9000000003',
          passwordHash,
          isActive: true,
          gender: 'Female',
          age: 26,
          designation: 'Store Manager',
          homeCity: 'Mumbai',
        },
      },
    },
  });

  // Locations (new, replacing rooms)
  const locationBLR = await prisma.location.create({
    data: { name: 'BLR', circle: 'South', address: 'Ballari', type: 'SATELLITE' },
  });

  // Create skills first
  const skillSA = await prisma.skill.create({
    data: { name: 'SA' },
  });
  const skillSM = await prisma.skill.create({
    data: { name: 'SM' },
  });

  // Create trainer user and trainer
  const trainer = await prisma.trainer.create({
    data: {
      isLeadFacilitator: true,
      user: {
        create: {
          name: "Deepa Menon",
          email: "<EMAIL>",
          phoneNumber: "9000000004",
          passwordHash,
          isActive: true
        }
      },
      location: {
        connect: { id: 1 }
      }
    }
  });

  // Link trainer to skills
  await prisma.trainerSkill.create({
    data: { trainerId: trainer.id, skillId: skillSA.id },
  });
  await prisma.trainerSkill.create({
    data: { trainerId: trainer.id, skillId: skillSM.id },
  });

  // Onboarding info
  await prisma.onboardingInfo.create({
    data: {
      candidateId: candidate1.id,
      employeeId: 'EMP1234',
      aadharName: 'RAVI KUMAR',
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Trainers
  await prisma.trainerAvailability.create({
    data: {
      trainerId: trainer.id,
      availableDate: new Date('2025-06-17'),
      isAvailable: true,
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Stores
  await prisma.store.create({
    data: {
      storeCode: 'BLR-KR01',
      region: 'South',
      storeName: 'Kormangala Optum',
      state: 'Karnataka',
      franchiseType: 'COCO',
      isOjtEnabled: true,
      currentLearnerCount: 3,
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });
  await prisma.store.create({
    data: {
      storeCode: 'MUM-AN01',
      region: 'West',
      storeName: 'Andheri Optum',
      state: 'Maharashtra',
      franchiseType: 'FOFO',
      isOjtEnabled: false,
      currentLearnerCount: 2,
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Ensure BLR location exists
  const blrLocation = await prisma.location.upsert({
    where: { name: 'BLR' },
    update: {},
    create: {
      name: 'BLR',
      circle: 'South',           // <-- required
      address: 'Bangalore, KA',  // <-- required
      type: 'ACADEMY',           // <-- required, must match your enum
    },
  });

  // LocationWorkingHours for BLR and MUM locations
  const blrWorkingHours = await prisma.locationWorkingHours.create({
    data: {
      location: { connect: { id: blrLocation.id } },
      startTime: '09:00',
      endTime: '18:00',
      lunchStartTime: '13:00',
      lunchEndTime: '14:00',
      isActive: true,
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  const mumLocation = await prisma.location.upsert({
    where: { name: 'MUM' },
    update: {},
    create: {
      name: 'MUM',
      circle: 'West',
      address: 'Mumbai, MH',
      type: 'ACADEMY',
    },
  });

  const mumWorkingHours = await prisma.locationWorkingHours.create({
    data: {
      location: { connect: { id: mumLocation.id } },
      startTime: '10:00',
      endTime: '19:00',
      lunchStartTime: '14:00',
      lunchEndTime: '15:00',
      isActive: true,
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Batches
  const batch = await prisma.batch.create({
    data: {
      batchName: 'SA_17_June',
      batchType: 'SA',
      batchDate: new Date('2025-06-17'),
      location: { connect: { id: blrLocation.id } },
      isLabBased: true,
      remarks: 'Planned for main center',
      candidates: {
        connect: [{ id: candidate1.id }, { id: candidate2.id }],
      },
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Assessments
  await prisma.assessment.create({
    data: {
      candidateId: candidate1.id,
      type: 'Pre-Eval',
      date: new Date('2025-06-17'),
      score: 82,
      result: 'Pass',
      evaluatorId: trainer.id,
      remarks: 'Good communication',
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Attendance
  await prisma.attendance.create({
    data: {
      candidateId: candidate1.id,
      batchId: batch.id,
      date: new Date('2025-06-17'),
      status: 'Present',
      photoUrl: 'https://domain.com/attend/cand_001.jpg',
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Travel info
  await prisma.travelInfo.create({
    data: {
      candidateId: candidate1.id,
      travelStatus: 'Confirmed',
      destination: 'Bangalore',
      ticketNumber: 'TICK1234',
      travelDate: new Date('2025-06-16'),
      modeOfTravel: 'Flight',
      travelFrom: 'Delhi',
      bookingStatus: 'Confirmed by LA Ops (Khurram)',
      dateOfBooking: new Date('2025-06-10'),
      fromLocation: 'Delhi',
      toLocation: 'Bangalore',
      quota: 'General',
      pnr: 'PNR1234',
      amount: 4500.00,
      ticketStatus: 'Booked',
      remarks: 'Window seat requested',
      createdAt: new Date(),
      createdBy: adminUser.id,
      updatedAt: new Date(),
      updatedBy: adminUser.id,
      deletedAt: null,
      deletedBy: null,
    },
  });

  // Access Control Seeding
  // 1. Create Permissions
  const permissions = [
    'view_candidates',
    'edit_candidates',
    'delete_candidates',
    'view_batches',
    'edit_batches',
    'delete_batches',
    'view_users',
    'edit_users',
    'delete_users',
    'manage_locations',
    'manage_modules',
    'manage_schedule_templates', // Added for schedule template management
    'manage_holidays',
    'manage_trainers',
    'manage_templates'
  ];
  const permissionRecords = await Promise.all(
    permissions.map((name) =>
      prisma.permission.create({
        data: {
          name,
          createdAt: new Date(),
          createdBy: adminUser.id,
          updatedAt: new Date(),
          updatedBy: adminUser.id,
          deletedAt: null,
          deletedBy: null,
        },
      })
    )
  );

  // 2. Create Roles
  const roles = ['admin', 'trainer', 'academy', 'candidate'];
  const roleRecords = await Promise.all(
    roles.map((name) =>
      prisma.role.create({
        data: {
          name,
          createdAt: new Date(),
          createdBy: adminUser.id,
          updatedAt: new Date(),
          updatedBy: adminUser.id,
          deletedAt: null,
          deletedBy: null,
        },
      })
    )
  );

  // 3. Assign Permissions to Roles
  // Admin gets all permissions
  const adminRole = roleRecords.find((r) => r.name === 'admin');
  if (!adminRole) throw new Error('admin role not found');
  for (const perm of permissionRecords) {
    await prisma.rolePermission.create({
      data: {
        roleId: adminRole.id,
        permissionId: perm.id,
        createdAt: new Date(),
        createdBy: adminUser.id,
        updatedAt: new Date(),
        updatedBy: adminUser.id,
        deletedAt: null,
        deletedBy: null,
      },
    });
  }
  // Trainer gets view/edit candidates and batches
  const trainerRole = roleRecords.find((r) => r.name === 'trainer');
  if (!trainerRole) throw new Error('trainer role not found');
  const trainerPerms = permissionRecords.filter((p) =>
    ['view_candidates', 'edit_candidates', 'view_batches', 'edit_batches'].includes(p.name)
  );
  for (const perm of trainerPerms) {
    await prisma.rolePermission.create({
      data: {
        roleId: trainerRole.id,
        permissionId: perm.id,
        createdAt: new Date(),
        createdBy: adminUser.id,
        updatedAt: new Date(),
        updatedBy: adminUser.id,
        deletedAt: null,
        deletedBy: null,
      },
    });
  }
  // Academy gets view permissions
  const academyRole = roleRecords.find((r) => r.name === 'academy');
  if (!academyRole) throw new Error('academy role not found');
  const academyPerms = permissionRecords.filter((p) =>
    p.name.startsWith('view_')
  );
  for (const perm of academyPerms) {
    await prisma.rolePermission.create({
      data: {
        roleId: academyRole.id,
        permissionId: perm.id,
        createdAt: new Date(),
        createdBy: adminUser.id,
        updatedAt: new Date(),
        updatedBy: adminUser.id,
        deletedAt: null,
        deletedBy: null,
      },
    });
  }

  // 4. Assign Roles to Users (example: assign admin to the created user)
  if (adminUser && adminRole) {
    await prisma.userRole.create({
      data: {
        userId: adminUser.id,
        roleId: adminRole.id,
        createdAt: new Date(),
        createdBy: adminUser.id,
        updatedAt: new Date(),
        updatedBy: adminUser.id,
        deletedAt: null,
        deletedBy: null,
      },
    });
  }

  // 5. Create Users for Candidates and assign 'candidate' role
  const candidateUsers = [];
  for (const cand of [candidate1, candidate2]) {
    // Link Candidate to User
    await prisma.candidate.update({
      where: { id: cand.id },
      data: { userId: cand.userId },
    });
    candidateUsers.push(cand);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 

// --- APPENDED SEED DATA FOR ADVANCED SCHEDULING SCENARIOS ---

async function appendAdvancedSeedData() {
  // 1. Add new modules
  const modules = await Promise.all([
    prisma.module.create({ data: { name: 'Retail Basics', duration: 3, nature: 'CRT', program: 'SA' } }),
    prisma.module.create({ data: { name: 'Customer Service', duration: 2, nature: 'CRT', program: 'SM' } }),
    prisma.module.create({ data: { name: 'Inventory Management', duration: 2, nature: 'OJT', program: 'SA' } }),
    prisma.module.create({ data: { name: 'Advanced Sales', duration: 4, nature: 'OJT', program: 'OPTOM' } }),
  ]);

  // 2. Add new trainers
  const newTrainerUsers = await Promise.all([
    prisma.user.create({ data: { name: 'Rajesh Sharma', email: '<EMAIL>', phoneNumber: '9000000005', passwordHash: await bcrypt.hash('password123', 10), isActive: true } }),
    prisma.user.create({ data: { name: 'Sumanth Rao', email: '<EMAIL>', phoneNumber: '9000000006', passwordHash: await bcrypt.hash('password123', 10), isActive: true } }),
    prisma.user.create({ data: { name: 'Alice Thomas', email: '<EMAIL>', phoneNumber: '9000000007', passwordHash: await bcrypt.hash('password123', 10), isActive: true } }),
    prisma.user.create({ data: { name: 'Bob Martin', email: '<EMAIL>', phoneNumber: '9000000008', passwordHash: await bcrypt.hash('password123', 10), isActive: true } }),
  ]);
  const newTrainers = await Promise.all([
    prisma.trainer.create({ data: { userId: newTrainerUsers[0].id, isLeadFacilitator: false, locationId: 1 } }),
    prisma.trainer.create({ data: { userId: newTrainerUsers[1].id, isLeadFacilitator: false, locationId: 1 } }),
    prisma.trainer.create({ data: { userId: newTrainerUsers[2].id, isLeadFacilitator: false, locationId: 1 } }),
    prisma.trainer.create({ data: { userId: newTrainerUsers[3].id, isLeadFacilitator: false, locationId: 1 } }),
  ]);

  // 3. TrainerModule mappings (skills)
  await Promise.all([
    // Rajesh: Retail Basics, Customer Service
    prisma.trainerModule.create({ data: { trainerId: newTrainers[0].id, moduleId: modules[0].id } }),
    prisma.trainerModule.create({ data: { trainerId: newTrainers[0].id, moduleId: modules[1].id } }),
    // Sumanth: Customer Service, Inventory Management
    prisma.trainerModule.create({ data: { trainerId: newTrainers[1].id, moduleId: modules[1].id } }),
    prisma.trainerModule.create({ data: { trainerId: newTrainers[1].id, moduleId: modules[2].id } }),
    // Alice: Inventory Management, Advanced Sales
    prisma.trainerModule.create({ data: { trainerId: newTrainers[2].id, moduleId: modules[2].id } }),
    prisma.trainerModule.create({ data: { trainerId: newTrainers[2].id, moduleId: modules[3].id } }),
    // Bob: Retail Basics, Advanced Sales
    prisma.trainerModule.create({ data: { trainerId: newTrainers[3].id, moduleId: modules[0].id } }),
    prisma.trainerModule.create({ data: { trainerId: newTrainers[3].id, moduleId: modules[3].id } }),
  ]);

  // 4. Add new location
  const mumLocation = await prisma.location.upsert({
    where: { name: 'MUM' },
    update: {},
    create: {
      name: 'MUM',
      circle: 'West',
      address: 'Mumbai, MH',
      type: 'ACADEMY',
    },
  });
  const blrLocation = await prisma.location.upsert({
    where: { name: 'BLR' },
    update: {},
    create: {
      name: 'BLR',
      circle: 'South',
      address: 'Bangalore, KA',
      type: 'ACADEMY',
    },
  });

  // 5. TrainerAvailability for 30 days (randomized)
  const today = new Date();
  for (const trainer of newTrainers) {
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      await prisma.trainerAvailability.create({
        data: {
          trainerId: trainer.id,
          availableDate: date,
          isAvailable: Math.random() > 0.2, // 80% chance available
        },
      });
    }
  }

  // 6. Holidays for both locations
  for (let i = 0; i < 3; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + 5 * (i + 1));
    await prisma.holiday.create({ data: { date, locationId: blrLocation.id, reason: `BLR Holiday ${i + 1}` } });
    await prisma.holiday.create({ data: { date, locationId: mumLocation.id, reason: `MUM Holiday ${i + 1}` } });
  }

  // Create a Template for group batch (SA_SM)
  const groupTemplate = await prisma.template.create({
    data: {
      name: 'SA_SM July Template',
      description: 'Template for SA_SM July group batch',
      batchType: 'SA_SM',
      version: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  });

  // Create a Template for OPTUM batch
  const optumTemplate = await prisma.template.create({
    data: {
      name: 'OPTUM July Template',
      description: 'Template for OPTUM July batch',
      batchType: 'OPTUM',
      version: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  });

  // 7. ScheduleTemplates for group and non-group batches
  // Group batch (SA+SM): both SA and SM on each day
  for (let i = 1; i <= 5; i++) {
    if (i % 2 === 0) {
      await prisma.scheduleTemplate.create({
        data: {
          dayNumber: i,
          moduleId: modules[(i - 1) % modules.length].id,
          groupType: 'SA',
          // Added fields per migration
          duration: 60,
          startTime: '10:00',
          endTime: '11:00',
          templateId: groupTemplate.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
      await prisma.scheduleTemplate.create({
        data: {
          dayNumber: i,
          moduleId: modules[(i + 1) % modules.length].id,
          groupType: 'SM',
          // Added fields per migration
          duration: 60,
          startTime: '11:00',
          endTime: '12:00',
          templateId: groupTemplate.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    } else {
      
      await prisma.scheduleTemplate.create({
        data: {
          dayNumber: i,
          moduleId: modules[i % modules.length].id,
          groupType: 'ALL',
          // Added fields per migration
          duration: 60,
          startTime: '10:00',
          endTime: '11:00',
          templateId: groupTemplate.id,
          createdAt: new Date(),
          updatedAt: new Date(),

        },
      });
    }
  }
  // Non-group batch (OPTUM)
  for (let i = 1; i <= 5; i++) {
    await prisma.scheduleTemplate.create({
      data: {
        dayNumber: i,
        moduleId: modules[(i + 1) % modules.length].id,
        groupType: 'ALL',
        // Added fields per migration
        duration: 60,
        startTime: '10:00',
        endTime: '11:00',
        templateId: optumTemplate.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  // 8. Add new batches
  const groupBatch = await prisma.batch.create({
    data: {
      batchName: 'SA_SM_July',
      batchType: 'SA_SM',
      batchDate: today,
      locationId: blrLocation.id,
      isLabBased: true,
      remarks: 'Group batch for July',
    },
  });
  const optumBatch = await prisma.batch.create({
    data: {
      batchName: 'OPTUM_July',
      batchType: 'OPTUM',
      batchDate: today,
      locationId: mumLocation.id,
      isLabBased: false,
      remarks: 'Optum batch for July',
    },
  });

  // 9. BatchModules (simulate past schedules for utilization)
  // Assign modules to groupBatch with different trainers
  for (let i = 0; i < 5; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    await prisma.batchModule.create({
      data: {
        batchId: groupBatch.id,
        moduleId: modules[i % modules.length].id,
        trainerId: newTrainers[i % newTrainers.length].id,
        locationId: blrLocation.id,
        date,
        groupType: i % 2 === 0 ? 'SA' : 'SM',
        startTime: new Date(date.setHours(9, 0, 0, 0)), //DATETIME(3) NOT NULL,from 10am to 11am
        endTime: new Date(date.setHours(10, 0, 0, 0)), //DATETIME(3) NOT NULL,
      },
    });
  }
  // Assign modules to optumBatch with different trainers
  for (let i = 0; i < 5; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    await prisma.batchModule.create({
      data: {
        batchId: optumBatch.id,
        moduleId: modules[(i + 1) % modules.length].id,
        trainerId: newTrainers[(i + 1) % newTrainers.length].id,
        locationId: mumLocation.id,
        date,
        groupType: 'ALL',
        startTime: new Date(date.setHours(10, 0, 0, 0)), //DATETIME(3) NOT NULL,from 10am to 11am
        endTime: new Date(date.setHours(11, 0, 0, 0)), //DATETIME(3) NOT NULL,
      },
    });
  }
}

appendAdvancedSeedData(); 