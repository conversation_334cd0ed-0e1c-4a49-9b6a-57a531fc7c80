{"name": "hiring-academy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "tsx migrations/migrate.ts", "prisma:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^5.12.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jsonwebtoken": "^9.0.10", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mysql2": "^3.9.7", "next": "15.3.3", "next-themes": "^0.4.6", "prisma": "^5.12.0", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-time-picker": "^7.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.3", "jest": "^30.0.2", "supertest": "^7.1.1", "tailwindcss": "^4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.5", "typescript": "^5", "ultracite": "5.0.36"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}