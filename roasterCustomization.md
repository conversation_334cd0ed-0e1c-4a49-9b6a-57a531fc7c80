# **Roster Customization Guide**

## **1. Schema Recap (Key Models for Customization)**

- **BatchModule**: Stores the assignment of a module, trainer, location, and date range for a batch.
- **TrainerModule**: Maps which trainers are eligible for which modules.
- **Holiday**: Stores global and location-specific holidays.
- **ScheduleTemplate**: The original plan for each batch type (not directly linked to BatchModule after roster creation).

---

## **2. Customization Levels & How to Implement**

### **A. Day Level Customization**
- **What:** Change trainer/module/holiday for a specific day in a batch.
- **How:**  
  - UI: Calendar/table view for each batch/trainer.
  - Action: Click a cell (day) → Edit trainer/module/mark as holiday/OJT.
  - DB: Update the relevant `BatchModule` record (change `trainerId`, `moduleId`, `startDate`, `endDate` as needed).
  - If marking as holiday, you may want to add a `Holiday` record for the location and date, and update affected `BatchModule` records.

### **B. Module Level Customization**
- **What:** Change the trainer or duration for an entire module in a batch.
- **How:**  
  - UI: Select a module row in the batch view.
  - Action: Edit trainer for all days of that module, or adjust start/end dates.
  - DB: Update all `BatchModule` records for that batch and module.

### **C. Batch Level Customization**
- **What:** Reschedule the entire batch, add/remove modules, or shift the schedule.
- **How:**  
  - UI: Batch settings page.
  - Action: Change batch start date, add/remove modules, shift all sessions.
  - DB: Update `Batch.batchDate`, add/remove `BatchModule` records, update dates.

### **D. Trainer Level Customization**
- **What:** Reassign all sessions for a trainer (e.g., if unavailable).
- **How:**  
  - UI: Trainer calendar view.
  - Action: Select a trainer, see all assignments, bulk reassign to another eligible trainer.
  - DB: Update `trainerId` in affected `BatchModule` records.

### **E. Location Level Customization**
- **What:** Add a new holiday, move a batch to another location.
- **How:**  
  - UI: Location holiday calendar.
  - Action: Add holiday for a location/date.
  - DB: Add `Holiday` record, update affected `BatchModule` records (skip or reschedule modules on new holiday).

---

## **3. UI/UX Suggestions**

### **A. Calendar/Table Views**
- **Trainer View:**  
  - Rows: Dates  
  - Columns: Batches/Modules  
  - Cells: Trainer assignment, module, status (holiday/OJT/free)
  - Click cell to edit assignment

- **Batch View:**  
  - Rows: Dates  
  - Columns: Modules  
  - Cells: Trainer assignment, status

- **Location Holiday Calendar:**  
  - Month view, add/edit holidays

### **B. Edit/Override Dialogs**
- When editing a cell, show a dialog:
  - Change trainer (dropdown of eligible trainers)
  - Change module (dropdown of modules)
  - Mark as holiday/OJT
  - Add notes/reason for override

### **C. Bulk Edit Tools**
- Select multiple days/modules/batches/trainers for bulk reassignment.

### **D. Manual Override Flag**
- Add a `manualOverride` boolean and `overrideReason` field to `BatchModule` (optional, for audit/logging).

---

## **4. Backend/API Patterns**

- **GET endpoints** for fetching roster by batch, trainer, or location.
- **PATCH/PUT endpoints** for updating a `BatchModule` (change trainer/module/dates).
- **POST endpoint** for adding a holiday (and logic to update affected modules).
- **Bulk update endpoints** for batch/module/trainer-level changes.

---

## **5. Example Customization Flows**

### **A. Change Trainer for a Day**
1. User clicks on a day in the trainer/batch calendar.
2. UI shows eligible trainers (from `TrainerModule`).
3. User selects a new trainer.
4. Backend updates the `trainerId` in the relevant `BatchModule` record.

### **B. Mark a Day as Holiday**
1. User clicks on a day in the location calendar.
2. UI prompts for holiday reason.
3. Backend adds a `Holiday` record for that location/date.
4. Backend updates affected `BatchModule` records (skip/reschedule modules as needed).

### **C. Shift Entire Batch**
1. User selects “Shift Batch” in batch view.
2. UI prompts for new start date.
3. Backend updates `Batch.batchDate` and all related `BatchModule` dates.

---

## **6. Audit & Logging (Recommended)**
- Track who made each change and when (add `updatedBy`, `updatedAt`, `overrideReason` to `BatchModule`).
- Show a change log in the UI for transparency.

---

## **7. Summary Table of Customization Actions**

| Level     | UI Action                | DB Change                        |
|-----------|--------------------------|----------------------------------|
| Day       | Edit cell in calendar    | Update one `BatchModule`         |
| Module    | Edit module row          | Update multiple `BatchModule`s   |
| Batch     | Shift batch, add/remove  | Update batch, add/remove modules |
| Trainer   | Bulk reassign sessions   | Update many `BatchModule`s       |
| Location  | Add holiday, move batch  | Add `Holiday`, update modules    |

---

## **8. Example Schema Extension (Optional)**

```prisma
model BatchModule {
  // ...existing fields...
  manualOverride Boolean @default(false)
  overrideReason String?
  updatedBy      Int?
  updatedAt      DateTime @updatedAt
}
```

---

## **9. Implementation Steps**

1. **Build calendar/table views for batch, trainer, and location.**
2. **Implement cell click/edit dialogs for day/module-level changes.**
3. **Add bulk edit tools for batch/trainer/module-level changes.**
4. **Add holiday management UI and logic to update affected modules.**
5. **Implement audit logging for all manual overrides.**
6. **Test all flows for edge cases (e.g., overlapping holidays, trainer unavailability).**

---

**If you want, I can provide:**
- Sample API endpoint specs
- UI wireframes
- Example code for any of the above

Let me know which part you want to dive deeper into!