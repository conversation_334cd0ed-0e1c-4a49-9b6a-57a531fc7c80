Great! Here's how to structure your **NoSQL setup** for the **Academy Batch Planning System**, using **MongoDB-style collections**. I'll give:

1. ✅ Collection names
2. 📄 Sample document structures
3. 🔗 Relationship logic (how documents connect)

---

## ✅ COLLECTIONS OVERVIEW

| Collection Name        | Description                                   |
| ---------------------- | --------------------------------------------- |
| `candidates`           | Candidate details (joining, job type, status) |
| `onboarding_info`      | Aadhar-based details from Oracle              |
| `assessments`          | Pre-eval and post-eval data                   |
| `trainers`             | Trainer profile data                          |
| `trainer_availability` | Trainer availability calendar                 |
| `rooms`                | Rooms/labs across centers                     |
| `room_availability`    | Room-wise availability                        |
| `stores`               | For OJT-based planning                        |
| `batches`              | Batches and linked trainees/trainers/rooms    |
| `attendance`           | Daily trainee attendance with verification    |
| `travel_info`          | Travel ticketing and booking details          |
| `users`                | Login & role-based access                     |

---

## 📂 SAMPLE DOCUMENT STRUCTURES

### 1. `candidates`

```json
{
  "_id": "cand_001",
  "fullName": "Ravi Kumar",
  "jobType": "SA",
  "personNumber": "P123456",
  "joiningDate": "2025-06-17",
  "joiningDay": "Monday",
  "status": "Accepted"
}
```

---

### 2. `onboarding_info`

```json
{
  "_id": "onb_001",
  "candidateId": "cand_001",
  "employeeId": "EMP1234",
  "aadharName": "RAVI KUMAR"
}
```

---

### 3. `assessments`

```json
{
  "_id": "assess_001",
  "candidateId": "cand_001",
  "type": "Pre-Eval",
  "date": "2025-06-17",
  "score": 82,
  "result": "Pass",
  "evaluatorId": "trainer_001",
  "remarks": "Good communication"
}
```

---

### 4. `trainers`

```json
{
  "_id": "trainer_001",
  "name": "Deepa Menon",
  "expertise": "SA, SM",
  "isLeadFacilitator": true
}
```

---

### 5. `trainer_availability`

```json
{
  "_id": "avail_001",
  "trainerId": "trainer_001",
  "availableDate": "2025-06-17",
  "isAvailable": true
}
```

---

### 6. `rooms`

```json
{
  "_id": "room_101",
  "roomName": "LA Lab 1",
  "location": "Bangalore",
  "roomType": "Lab",
  "centerType": "Main"
}
```

---

### 7. `room_availability`

```json
{
  "_id": "roomavail_001",
  "roomId": "room_101",
  "availableDate": "2025-06-17",
  "isAvailable": true
}
```

---

### 8. `stores`

```json
{
  "_id": "store_001",
  "storeCode": "BLR-KR01",
  "region": "South",
  "storeName": "Kormangala Optum",
  "isOjtEnabled": true,
  "currentLearnerCount": 3
}
```

---

### 9. `batches`

```json
{
  "_id": "batch_001",
  "batchName": "SA_17_June",
  "batchDate": "2025-06-17",
  "jobType": "SA",
  "trainerId": "trainer_001",
  "roomId": "room_101",
  "candidateIds": ["cand_001", "cand_002", "cand_003"],
  "isLabBased": true,
  "isOjt": false,
  "remarks": "Planned for main center"
}
```

---

### 10. `attendance`

```json
{
  "_id": "att_001",
  "candidateId": "cand_001",
  "batchId": "batch_001",
  "date": "2025-06-17",
  "status": "Present",
  "photoUrl": "https://domain.com/attend/cand_001.jpg"
}
```

---

### 11. `travel_info`

```json
{
  "_id": "travel_001",
  "candidateId": "cand_001",
  "travelStatus": "Booked",
  "destination": "Bangalore",
  "ticketNumber": "TK34562",
  "travelDate": "2025-06-16"
}
```

---

### 12. `users`

```json
{
  "_id": "user_001",
  "name": "Sneha Rao",
  "email": "<EMAIL>",
  "role": "Academy" // Other values: Trainer, Recruiter, Travel, Admin, Trainee
}
```

---

## 🔗 RELATIONSHIP EXAMPLES (JOIN-LIKE LOGIC IN NoSQL)

- `candidates` → embedded in `batches` via `candidateIds[]`
- `trainer_availability` → filtered using `trainerId`
- `assessments` → fetched using `candidateId`
- `room_availability` → filtered by `roomId`
- `travel_info` → filtered by `candidateId`
- `attendance` → filtered by both `candidateId` and `batchId`

---

## ✅ BONUS: Collections Indexing Suggestions

- Candidates: `personNumber`, `joiningDate`
- Assessments: `candidateId`, `type`
- Batches: `batchDate`, `trainerId`
- Availability: `trainerId`, `availableDate`
- Attendance: compound index on `candidateId + date`
