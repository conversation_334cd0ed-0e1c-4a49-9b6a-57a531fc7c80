version: "3.8"
services:
  mysql:
    image: mysql:8.4
    container_name: mysql
    restart: always
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: hiring_academy
      MYSQL_USER: ha_user
      MYSQL_PASSWORD: ha_password
    volumes:
      - mysql_data:/var/lib/mysql
  app:
    build: .
    container_name: hiring-academy-app
    restart: always
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=mysql://ha_user:ha_password@mysql:3307/hiring_academy
    depends_on:
      - mysql
volumes:
  mysql_data:
