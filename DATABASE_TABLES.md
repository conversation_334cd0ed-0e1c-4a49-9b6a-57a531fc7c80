# Database Tables Overview

Below is a summary of each table (model) in the database and its purpose.

---

## Candidate
Represents a candidate (trainee) in the system, including their personal and onboarding details, assessments, attendance, batch assignments, and a unique phone number.

## OnboardingInfo
Stores onboarding information for each candidate, such as employee ID and <PERSON><PERSON><PERSON> name.

## Assessment
Records assessments taken by candidates, including type, date, score, result, evaluator, and remarks.

## Trainer
Represents a trainer/facilitator, including their expertise, availability, batches they handle, and a unique phone number.

## TrainerAvailability
Tracks the availability of trainers on specific dates.

## Room
Represents a physical or virtual room/location where batches are conducted, including its type and availability.

## RoomAvailability
Tracks the availability of rooms on specific dates.

## Store
Represents a store location, including region, store code, and whether OJT (on-the-job training) is enabled.

## Batch
Represents a training batch, including its name, date, job type, assigned trainer, room, candidates, and attendance records.

## Attendance
Tracks attendance of candidates for each batch on specific dates, including status and optional photo evidence.

## TravelInfo
Stores travel details for candidates, such as travel status, destination, ticket number, and travel date.

## User
Represents a system user (for authentication and access control), including their name, email, assigned roles, and a unique phone number.

## Role
Defines a role in the access control system (e.g., admin, trainer, manager).

## Permission
Defines a permission in the access control system (e.g., view candidates, edit assessments).

## UserRole
Associates users with roles (many-to-many relationship).

## RolePermission
Associates roles with permissions (many-to-many relationship). 