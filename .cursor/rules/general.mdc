---
description: 
globs: 
alwaysApply: true
---
Use shadcn Ui for building front-end code
Ui should responsive for all mobile, desktop and tabs

Goals
Reusable, well-structured code.
Show success/error messages after API calls use toast component it is existing already.
Follow accessibility, type safety, and code quality rules (as per your workspace rules).
Use shadcn/ui components and ensure responsiveness.
fix all linter issues if you found.