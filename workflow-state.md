# Academy

## State
Phase: ANALYZE
Status: IN_PROGRESS
CurrentItem: 4

## Plan
### Task 3: List Permissions API Blueprint

#### 1. **API Endpoint Design**
- **Method:** GET
- **Path:** `/api/permissions`
- **Purpose:** Return paginated, filtered, and sorted list of permissions

#### 2. **Request Structure**
```typescript
// Query Parameters (all optional)
interface PermissionsQuery {
  page?: number;        // Default: 1, Min: 1
  limit?: number;       // Default: 10, Min: 1, Max: 100
  name?: string;        // Partial match filter
  sort?: 'id' | 'name' | 'createdAt' | 'updatedAt'; // Default: 'name'
  order?: 'asc' | 'desc'; // Default: 'asc'
}
```

#### 3. **Authentication & Authorization**
- Extract JWT token from `token` cookie using existing pattern
- Verify token and decode payload
- Check user has `view_permissions` permission (to be added to seed data)
- Return 401 if unauthorized

#### 4. **Input Validation Schema (Zod)**
```typescript
const PermissionsQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
  sort: z.enum(['id', 'name', 'createdAt', 'updatedAt']).default('name'),
  order: z.enum(['asc', 'desc']).default('asc'),
});
```

#### 5. **Database Query Logic**
```typescript
// Build Prisma query with:
// - WHERE clause for name filtering (contains, case-insensitive)
// - WHERE clause to exclude soft-deleted records (deletedAt: null)
// - ORDER BY for sorting
// - SKIP/TAKE for pagination
// - Count query for total records
```

#### 6. **Response Format**
```typescript
interface PermissionsResponse {
  success: true;
  data: {
    permissions: Array<{
      id: number;
      name: string;
      createdAt: string;
      updatedAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}
```

#### 7. **Implementation Steps**
1. Create `/src/app/api/permissions/route.ts`
2. Import dependencies (NextResponse, z, PrismaClient, jwt)
3. Create validation schema
4. Implement authentication helper function (reusable)
5. Build GET handler with:
   - JWT authentication
   - Query parameter validation
   - Database query construction
   - Pagination calculation
   - Response formatting
6. Create comprehensive test file
7. Add `view_permissions` permission to seed data

#### 8. **Error Handling**
- 400: Invalid query parameters
- 401: Missing/invalid JWT token or insufficient permissions
- 500: Database or server errors
- Consistent error response format

#### 9. **Testing Strategy**
- Test pagination (first page, middle page, last page)
- Test filtering by name (partial match, no results)
- Test sorting (by each field, asc/desc)
- Test authentication (no token, invalid token, insufficient permissions)
- Test edge cases (invalid parameters, empty database)

#### 10. **Security Considerations**
- No sensitive data exposure
- Input sanitization
- SQL injection prevention (Prisma handles this)
- Rate limiting ready structure

## Implementation Guidelines
- **Reusable Code Structure**: All APIs must follow a consistent, optimized architecture with shared components
- **Standard Response Format**: Implement unified response structure for all endpoints (success/error format)
- **Pagination**: All listing APIs must include pagination with configurable page size and offset/cursor support
- **Filtering**: All listing APIs must support field-based filtering with standardized query parameters
- **Sorting**: All listing APIs must support multi-field sorting with asc/desc options
- **Error Handling**: Implement centralized error handling with consistent error codes and messages
- **Validation**: Use shared validation middleware for input sanitization and schema validation
- **Authorization**: Implement role-based access control middleware that can be reused across endpoints
- **Database Optimization**: Use efficient queries with proper indexing, joins, and select optimization
- **Caching**: Implement appropriate caching strategies for frequently accessed data
- **Rate Limiting**: Apply consistent rate limiting across all endpoints
- **Logging**: Standardized request/response logging and error tracking
- **Schedule Flexibility**: Schedule event updates must support flexible location and personnel changes:
  - Location changes: Room ↔ Store (bidirectional conversion)
  - Personnel changes: Trainer ↔ Store Manager (role-based assignment)
  - Time validation: Only allow updates before event start time
  - Conflict detection: Check for scheduling conflicts when making changes

### Task 2: ME API Analysis
- **Purpose:** Provide an endpoint `/api/me` that reads the JWT token from the request header cookie, verifies it, and returns the decoded payload (user info, roles, permissions, etc.).
- **Input:**
  - HTTP GET request to `/api/me`.
  - JWT token is sent as an HttpOnly cookie named `token`.
- **Output:**
  - On valid JWT: Return decoded payload (user id, name, email, roles, permissions, etc.) as JSON.
  - On missing/invalid/expired JWT: Return 401 Unauthorized with error message.
- **Edge Cases:**
  - No cookie present.
  - Cookie present but not named `token`.
  - Malformed or expired JWT.
  - JWT present but missing required fields.
- **Security:**
  - Do not leak sensitive error details.
  - Only return the JWT payload, do not re-issue or refresh the token.

## Rules
- [PHASE: ANALYZE]: Only analyze, do not plan or code.
- [PHASE: BLUEPRINT]: Write a detailed plan, no code yet.
- [PHASE: CONSTRUCT]: Implement the plan, update log after each step.
- [PHASE: VALIDATE]: Test the implementation, log results.
- After VALIDATE, if more items in ## Items, set CurrentItem to next and repeat.
- Always update this file after every action.

## Items
| TaskID | Action                | Endpoint/Method         | Description                        |
|--------|-----------------------|-------------------------|------------------------------------|
| 1      | Login API             | /api/login              | where different types of users login to the system|
| 2      | ME API                | /api/me                 | reads request header cookie jwt-token and returns payload |
| 3      | List Permissions API  | GET /api/permissions    | List all permissions with pagination, filtering, sorting |
| 4      | Create Permission API | POST /api/permissions   | Create permission (system admin only) |
| 5      | Update Permission API | PUT /api/permissions/{id} | Update permission details |
| 6      | Delete Permission API | DELETE /api/permissions/{id} | Delete permission |
| 7      | Create Role API       | POST /api/roles         | Create role with permissions (admin only) |
| 8      | List Roles API        | GET /api/roles          | List all roles with pagination, filtering, sorting |
| 9      | Get Role API          | GET /api/roles/{id}     | Get role by ID with permissions |
| 10     | Update Role API       | PUT /api/roles/{id}     | Update role and permissions |
| 11     | Delete Role API       | DELETE /api/roles/{id}  | Delete role (admin only) |
| 12     | Create User API       | POST /api/users         | Create user with role-based access (admin only) |
| 13     | List Users API        | GET /api/users          | List users with pagination, filtering, sorting, role-based access |
| 14     | Get User API          | GET /api/users/{id}     | Get user by ID with role-based access |
| 15     | Update User API       | PUT /api/users/{id}     | Update user information with validation |
| 16     | Delete User API       | DELETE /api/users/{id}  | Soft delete user (admin only) |
| 17     | Assign User Role API  | POST /api/users/{id}/roles | Assign role to user |
| 18     | Remove User Role API  | DELETE /api/users/{id}/roles/{roleId} | Remove role from user |
| 19     | Get User Roles API    | GET /api/users/{id}/roles | Get user's roles with pagination |
| 20     | Create Candidate API  | POST /api/candidates    | Create candidate with user account |
| 21     | List Candidates API   | GET /api/candidates     | List candidates with pagination, filtering (status, batch, recruiter), sorting |
| 22     | Get Candidate API     | GET /api/candidates/{id} | Get candidate by ID with full details |
| 23     | Update Candidate API  | PUT /api/candidates/{id} | Update candidate information |
| 24     | Delete Candidate API  | DELETE /api/candidates/{id} | Soft delete candidate |
| 25     | Create Onboarding API | POST /api/candidates/{id}/onboarding | Create onboarding information |
| 26     | Get Onboarding API    | GET /api/candidates/{id}/onboarding | Get onboarding status and details |
| 27     | Update Onboarding API | PUT /api/candidates/{id}/onboarding | Update onboarding information |
| 28     | Create Trainer API    | POST /api/trainers      | Create trainer with user account |
| 29     | List Trainers API     | GET /api/trainers       | List trainers with pagination, filtering (expertise), sorting |
| 30     | Get Trainer API       | GET /api/trainers/{id}  | Get trainer by ID with details |
| 31     | Update Trainer API    | PUT /api/trainers/{id}  | Update trainer information |
| 32     | Delete Trainer API    | DELETE /api/trainers/{id} | Soft delete trainer |
| 33     | Set Trainer Availability API | POST /api/trainers/{id}/availability | Set trainer availability (trainer or admin) |
| 34     | Get Trainer Availability API | GET /api/trainers/{id}/availability | Get trainer availability with date range filtering, pagination |
| 35     | Update Trainer Availability API | PUT /api/trainers/{id}/availability/{availId} | Update availability slot |
| 36     | Remove Trainer Availability API | DELETE /api/trainers/{id}/availability/{availId} | Remove availability slot |
| 37     | Create Batch API      | POST /api/batches       | Create batch without trainer or location assignment |
| 38     | List Batches API      | GET /api/batches        | List batches with pagination, filtering (date, trainer, room, status), sorting |
| 39     | Get Batch API         | GET /api/batches/{id}   | Get batch by ID with candidates and schedule |
| 40     | Update Batch API      | PUT /api/batches/{id}   | Update batch details |
| 41     | Cancel Batch API      | DELETE /api/batches/{id} | Cancel batch |
| 42     | Create Schedule API   | POST /api/batches/{id}/schedule | Create schedule for batch with room/store, time duration, and trainer assignment |
| 43     | Get Batch Schedule API | GET /api/batches/{id}/schedule | Get batch schedule and timeline |
| 44     | Update Batch Schedule API | PUT /api/batches/{id}/schedule | Update batch schedule |
| 45     | Update Schedule Event API | PUT /api/batches/{id}/schedule/{scheduleId} | Update individual schedule event - can change room to store, trainer to store manager |
| 46     | Delete Schedule Event API | DELETE /api/batches/{id}/schedule/{scheduleId} | Remove schedule event |
| 47     | Add Batch Candidates API | POST /api/batches/{id}/candidates | Add candidates to batch |
| 48     | Remove Batch Candidate API | DELETE /api/batches/{id}/candidates/{candidateId} | Remove candidate from batch |
| 49     | List Batch Candidates API | GET /api/batches/{id}/candidates | List batch candidates with pagination, sorting |
| 50     | Create Room API       | POST /api/rooms         | Create room with capacity and features |
| 51     | List Rooms API        | GET /api/rooms          | List rooms with pagination, filtering (availability), sorting |
| 52     | Get Room API          | GET /api/rooms/{id}     | Get room by ID with details |
| 53     | Update Room API       | PUT /api/rooms/{id}     | Update room details |
| 54     | Delete Room API       | DELETE /api/rooms/{id}  | Delete room |
| 55     | Set Room Availability API | POST /api/rooms/{id}/availability | Set room availability |
| 56     | Get Room Availability API | GET /api/rooms/{id}/availability | Get room availability with date range filtering, pagination |
| 57     | Update Room Availability API | PUT /api/rooms/{id}/availability/{availId} | Update availability slot |
| 58     | Remove Room Availability API | DELETE /api/rooms/{id}/availability/{availId} | Remove availability slot |
| 59     | Create Store API      | POST /api/stores        | Create store with location and details |
| 60     | List Stores API       | GET /api/stores         | List stores with pagination, filtering (region, franchise type), sorting |
| 61     | Get Store API         | GET /api/stores/{id}    | Get store by ID with details |
| 62     | Update Store API      | PUT /api/stores/{id}    | Update store information |
| 63     | Delete Store API      | DELETE /api/stores/{id} | Soft delete store |
| 64     | Create Assessment API | POST /api/assessments   | Create assessment for candidate |
| 65     | List Assessments API  | GET /api/assessments    | List assessments with pagination, filtering (candidate, evaluator, date), sorting |
| 66     | Get Assessment API    | GET /api/assessments/{id} | Get assessment by ID with details |
| 67     | Update Assessment API | PUT /api/assessments/{id} | Update assessment (evaluator only) |
| 68     | Delete Assessment API | DELETE /api/assessments/{id} | Delete assessment |
| 69     | Mark Attendance API   | POST /api/attendance    | Mark attendance with photo upload |
| 70     | List Attendance API   | GET /api/attendance     | Get attendance records with pagination, filtering (batch, candidate, date), sorting |
| 71     | Update Attendance API | PUT /api/attendance/{id} | Update attendance status |
| 72     | Get Attendance Report API | GET /api/attendance/reports | Get attendance report for batch/candidate with filtering |
| 73     | Create Travel API     | POST /api/candidates/{id}/travel | Create travel information |
| 74     | Get Travel API        | GET /api/candidates/{id}/travel | Get travel details |
| 75     | Update Travel API     | PUT /api/candidates/{id}/travel | Update travel status and details |
| 76     | Cancel Travel API     | DELETE /api/candidates/{id}/travel | Cancel travel |
| 77     | Get Batch Reports API | GET /api/reports/batches | Generate batch completion reports with filtering, pagination |
| 78     | Get Trainer Reports API | GET /api/reports/trainers | Generate trainer utilization reports with filtering, pagination |
| 79     | Get Candidate Reports API | GET /api/reports/candidates | Generate candidate progress reports with filtering, pagination |
| 80     | Get Assessment Analytics API | GET /api/reports/assessments | Generate assessment analytics with filtering, pagination |
| 81     | Get Attendance Summary API | GET /api/reports/attendance | Generate attendance summaries with filtering, pagination |
| 82     | Upload Attendance Photos API | POST /api/upload/attendance | Upload attendance photos with validation |
| 83     | Upload Candidate Docs API | POST /api/upload/candidates | Upload candidate documents with validation |
| 84     | Upload Assessment Materials API | POST /api/upload/assessments | Upload assessment materials with validation |

## Completed Items
| TaskID | Action        | Endpoint/Method   | Description              |
|--------|---------------|-------------------|--------------------------|
| 1      | Login API     | /api/login        | where different types of users login to the system|
| 2      | ME API        | /api/me           | reads request header cookie jwt-token and returns payload |
| 3      | List Permissions API  | GET /api/permissions    | List all permissions with pagination, filtering, sorting |

## Log
- [2024-06-18] Entered BLUEPRINT phase for Login API. Drafted detailed implementation plan for /api/login endpoint, covering requirements, API design, business logic, security, and testing. Awaiting user approval before proceeding to CONSTRUCT phase.
- [2024-06-18] Updated plan per user: JWT token must be set as HttpOnly cookie with all permissions, user name, and email address; response body must indicate success.
- [2024-06-18] User approved blueprint. Transitioning to CONSTRUCT phase for Login API. Implementation in progress.
- [2024-06-18] Login API implemented: POST /api/login validates input, checks user, verifies password, issues JWT with all permissions, roles, user name, and email, sets as HttpOnly cookie, and returns success response. Ready for validation.
- [2024-06-18] Transitioned to VALIDATE phase for Login API. Validation plan drafted and in progress.
- [2024-06-18] Login API validated: all tests passed, endpoint and test data seeding confirmed. Marked as completed. Ready for next workflow item.
- [2024-06-18] Started ANALYZE phase for Task 2 (ME API): requirements, input/output, and edge cases documented in plan.
- [2024-06-18] Entered BLUEPRINT phase for Task 2 (ME API). Drafted detailed implementation plan for /api/me endpoint. Awaiting user approval.
- [2024-06-18] User approved blueprint. Transitioning to CONSTRUCT phase for Task 2 (ME API). Implementation in progress.
- [2024-06-18] /api/me endpoint implemented: reads JWT from cookie, verifies and decodes, returns payload or 401. Ready for validation.
- [2024-07-04] ME API validated: all 4 tests passed (401 for missing token, 401 for invalid token, 401 for expired token, 200 with payload for valid token). Task 2 completed successfully.
- [2024-07-04] Started ANALYZE phase for Task 3 (List Permissions API): analyzing requirements for GET /api/permissions endpoint with pagination, filtering, and sorting capabilities.
- [2024-07-04] Completed ANALYZE phase for Task 3: database structure analyzed, existing permissions identified, requirements fully documented.
- [2024-07-04] Entered BLUEPRINT phase for Task 3 (List Permissions API): detailed implementation plan created with 10 comprehensive sections covering API design, authentication, validation, database queries, response format, testing strategy, and security considerations. Awaiting user approval before proceeding to CONSTRUCT phase.
- [2024-07-04] List Permissions API implemented: GET /api/permissions with pagination, filtering, sorting, JWT authentication, and validation. All 13 tests passed. Task 3 completed successfully.
- [2024-07-04] Started ANALYZE phase for Task 4 (Create Permission API): analyzing requirements for POST /api/permissions endpoint.

## Workflow History
- 9acb89d: Implement and validate Login API endpoint with JWT, roles, and permissions; add comprehensive tests and workflow state tracking

## ArchiveLog
<!-- RULE_LOG_ROTATE_01 stores condensed summaries here -->

## Blueprint History
<!-- RULE_BLUEPRINT_ARCHIVE_01 stores previous blueprint versions here -->
<!-- Format: ### Blueprint [YYYY-MM-DD HH:MM:SS] - ID: [UUID-short] -->
<!-- Each archived blueprint is stored under its timestamped heading -->