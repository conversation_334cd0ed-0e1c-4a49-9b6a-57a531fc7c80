'use client';

import type { ReactNode } from 'react';

import Spinner from '@/components/ui/spinner';
import { useAuth } from '@/hooks/useAuth';
import { useAuthStore } from '@/store/authStore';

// components/AuthProvider.tsx

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { isLoading } = useAuthStore();

  // This will trigger the /me API call on mount and page refresh
  useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <Spinner />;
  }

  return <>{children}</>;
};
