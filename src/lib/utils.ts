import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getIpFromRequest(request: Request): string | undefined {
  // Try to get the x-forwarded-for header (may be a comma-separated list)
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    // The first IP in the list is the original client
    return forwarded.split(',')[0].trim();
  }
  // Fallback: not available in Next.js App Router, but you could try request.headers.get('x-real-ip')
  const realIp = request.headers.get('x-real-ip');
  if (realIp) {
    return realIp;
  }
  // No IP found
  return;
}
