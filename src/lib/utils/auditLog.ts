import { PrismaClient, Prisma } from '@prisma/client';

export async function logAudit({
  prisma,
  action,
  tableName,
  recordId,
  userId,
  oldValue,
  newValue,
  metadata,
}: {
  prisma: PrismaClient;
  action: string;
  tableName: string;
  recordId: string;
  userId?: number;
  oldValue?: Prisma.InputJsonValue;
  newValue?: Prisma.InputJsonValue;
  metadata?: Prisma.InputJsonValue;
}) {
  await prisma.auditLog.create({
    data: {
      action,
      tableName,
      recordId,
      userId,
      oldValue,
      newValue,
      metadata,
    },
  });
} 