import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret';

export interface JWTPayload {
  id: number;
  name: string;
  email: string;
  roles: string[];
  permissions: string[];
}

export function getTokenFromCookie(cookieHeader?: string | null): string | null {
  if (!cookieHeader) return null;
  const cookies = cookieHeader.split(';').map((c) => c.trim());
  for (const cookie of cookies) {
    if (cookie.startsWith('token=')) {
      return cookie.substring('token='.length);
    }
  }
  return null;
}

export function getTokenFromAuthHeader(authHeader?: string | null): string | null {
  if (!authHeader?.startsWith('Bearer ')) return null;
  return authHeader.substring('Bearer '.length);
}

export function verifyJWT(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch {
    return null;
  }
}

export function hasPermission(payload: JWTPayload | null, permission: string): boolean {
  return (
    !!payload && Array.isArray(payload.permissions) && payload.permissions.includes(permission)
  );
}

export async function authenticateAndAuthorize(
  req: Request,
  requiredPermission: string,
): Promise<{ success: boolean; error?: string; userId?: number; payload?: JWTPayload }> {
  try {
    // Check Authorization header first
    const authHeader = req.headers.get('Authorization');
    let token = getTokenFromAuthHeader(authHeader);

    // If no valid token in Authorization header, try cookie
    if (!token) {
    const cookieHeader = req.headers.get('cookie');
      token = getTokenFromCookie(cookieHeader);
    }

    if (!token) {
      return { success: false, error: 'Authentication required' };
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return { success: false, error: 'Invalid or expired token' };
    }

    if (!hasPermission(payload, requiredPermission)) {
      return { success: false, error: 'Insufficient permissions' };
    }

    return { success: true, userId: payload.id, payload };
  } catch (err) {
    console.error('Authentication error:', err);
    return { success: false, error: 'Authentication failed' };
  }
}
