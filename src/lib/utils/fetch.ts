// HTTP Method types
type HttpMethod =
  | 'GET'
  | 'POST'
  | 'PUT'
  | 'PATCH'
  | 'DELETE'
  | 'HEAD'
  | 'OPTIONS';

// Fetch options interface
interface FetchOptions {
  method?: HttpMethod;
  body?: object | FormData | string;
  headers?: Record<string, string>;
  params?: Record<string, string | number | boolean>;
  timeout?: number;
  responseType?: 'json' | 'text' | 'blob' | 'arrayBuffer' | 'formData';
}

// Configuration
const DEFAULT_TIMEOUT = 30_000; // 30 seconds

/**
 * Build URL with query parameters
 * Handles both SSR and CSR environments
 */
const buildUrl = (
  endpoint: string,
  params?: Record<string, string | number | boolean>
): string => {
  let finalUrl: string;

  // If it's an absolute URL, use it as-is
  if (endpoint.startsWith('http')) {
    finalUrl = endpoint;
  } else if (typeof window !== 'undefined') {
    // Client-side: use window.location.origin
    finalUrl = new URL(`/api${endpoint}`, window.location.origin).toString();
  } else {
    // Server-side: just use the relative path
    // Next.js will handle this correctly in SSR
    finalUrl = endpoint;
  }

  // Add query parameters if they exist
  if (params && Object.keys(params).length > 0) {
    const url = new URL(
      finalUrl,
      typeof window !== 'undefined'
        ? window.location.origin
        : 'http://localhost:3000'
    );

    for (const [key, value] of Object.entries(params)) {
      if (value != null) {
        url.searchParams.append(key, String(value));
      }
    }

    return typeof window !== 'undefined'
      ? url.toString()
      : `${url.pathname}${url.search}`;
  }

  return finalUrl;
};

/**
 * Create timeout promise
 */
const createTimeoutPromise = (timeout: number): Promise<never> => {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), timeout);
  });
};

/**
 * Generic response parser
 */
const parseResponse = async <T>(
  response: Response,
  responseType?: string
): Promise<T> => {
  // If responseType is specified, use it
  if (responseType) {
    switch (responseType) {
      case 'json':
        return (await response.json()) as T;
      case 'text':
        return (await response.text()) as T;
      case 'blob':
        return (await response.blob()) as T;
      case 'arrayBuffer':
        return (await response.arrayBuffer()) as T;
      case 'formData':
        return (await response.formData()) as T;
      default:
        return (await response.json()) as T;
    }
  }

  // Auto-detect based on content type
  const contentType = response.headers.get('content-type')?.toLowerCase() || '';

  if (contentType.includes('application/json')) {
    return (await response.json()) as T;
  }

  if (contentType.includes('text/')) {
    return (await response.text()) as T;
  }

  if (
    contentType.includes('application/octet-stream') ||
    contentType.includes('image/') ||
    contentType.includes('video/') ||
    contentType.includes('audio/')
  ) {
    return (await response.blob()) as T;
  }

  // Default to JSON for unknown types
  try {
    return (await response.json()) as T;
  } catch {
    return (await response.text()) as T;
  }
};

/**
 * Main fetch utility function
 */
export const fetchApi = async <T = unknown>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<T> => {
  const {
    method = 'GET',
    body,
    headers = {},
    params,
    timeout = DEFAULT_TIMEOUT,
    responseType,
  } = options;

  const url = buildUrl(endpoint, params);

  // Default headers
  const defaultHeaders: Record<string, string> = {
    ...(!(body instanceof FormData) && { 'Content-Type': 'application/json' }),
    ...headers,
  };

  // Request configuration
  const requestConfig: RequestInit = {
    method,
    headers: defaultHeaders,
  };

  // Add body for non-GET requests
  if (body && method !== 'GET') {
    requestConfig.body =
      body instanceof FormData || typeof body === 'string'
        ? body
        : JSON.stringify(body);
  }

  try {
    // Create fetch promise with timeout
    const fetchPromise = fetch(url, requestConfig);
    const timeoutPromise = createTimeoutPromise(timeout);

    const response = (await Promise.race([
      fetchPromise,
      timeoutPromise,
    ])) as Response;

    // Handle HTTP errors
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

      try {
        const errorData = (await response.json()) as {
          message?: string;
          error?: string;
        };
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch {
        // If response is not JSON, keep the default error message
      }

      throw new Error(errorMessage);
    }

    // Use generic response parser
    return await parseResponse<T>(response, responseType);
  } catch (error) {
    const errorObj = error as Error;

    // Enhanced error information
    if (errorObj.message.includes('Failed to fetch')) {
      throw new Error('Network error: Please check your internet connection');
    }

    throw errorObj;
  }
};

/**
 * Convenience methods for different HTTP methods
 */
export const api = {
  get: <T = unknown>(
    endpoint: string,
    options?: {
      params?: Record<string, string | number | boolean>;
      responseType?: FetchOptions['responseType'];
    }
  ) => fetchApi<T>(endpoint, { method: 'GET', ...options }),

  post: <T = unknown>(
    endpoint: string,
    body?: object | FormData | string,
    options?: {
      headers?: Record<string, string>;
      params?: Record<string, string | number | boolean>;
      responseType?: FetchOptions['responseType'];
    }
  ) => fetchApi<T>(endpoint, { method: 'POST', body, ...options }),

  put: <T = unknown>(
    endpoint: string,
    body?: object | FormData | string,
    options?: {
      headers?: Record<string, string>;
      params?: Record<string, string | number | boolean>;
      responseType?: FetchOptions['responseType'];
    }
  ) => fetchApi<T>(endpoint, { method: 'PUT', body, ...options }),

  patch: <T = unknown>(
    endpoint: string,
    body?: object | FormData | string,
    options?: {
      headers?: Record<string, string>;
      params?: Record<string, string | number | boolean>;
      responseType?: FetchOptions['responseType'];
    }
  ) => fetchApi<T>(endpoint, { method: 'PATCH', body, ...options }),

  delete: <T = unknown>(
    endpoint: string,
    options?: { responseType?: FetchOptions['responseType'] }
  ) => fetchApi<T>(endpoint, { method: 'DELETE', ...options }),
};
