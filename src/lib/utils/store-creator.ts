import { create, type StateCreator } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

// Basic store creator with DevTools
export const createStore = <T>(
  stateCreator: StateCreator<T>,
  storeName: string
) => {
  return create<T>()(
    devtools(stateCreator, {
      name: storeName,
      enabled: process.env.NODE_ENV === 'development',
      trace: true,
    })
  );
};

// Store creator with persistence and DevTools
export const createPersistedStore = <T>(
  stateCreator: StateCreator<T>,
  storeName: string,
  persistOptions?: {
    partialize?: (state: T) => Partial<T>;
    version?: number;
  }
) => {
  return create<T>()(
    devtools(
      persist(stateCreator, {
        name: `${storeName}-storage`,
        storage: createJSONStorage(() => localStorage),
        ...persistOptions,
      }),
      {
        name: storeName,
        enabled: process.env.NODE_ENV === 'development',
        trace: true,
      }
    )
  );
};

// Advanced store creator with all middleware options
export const createAdvancedStore = <T>(
  stateCreator: StateCreator<T>,
  options: {
    name: string;
    persist?: boolean;
    persistOptions?: {
      partialize?: (state: T) => Partial<T>;
      version?: number;
    };
    devtools?: boolean;
    devtoolsOptions?: {
      enabled?: boolean;
      trace?: boolean;
    };
  }
) => {
  let store = stateCreator;

  // Add persistence if requested
  if (options.persist) {
    store = persist(store, {
      name: `${options.name}-storage`,
      storage: createJSONStorage(() => localStorage),
      ...options.persistOptions,
    }) as StateCreator<T>;
  }

  // Add devtools if requested (default: true in development)
  if (options.devtools !== false) {
    store = devtools(store, {
      name: options.name,
      enabled:
        options.devtoolsOptions?.enabled ??
        process.env.NODE_ENV === 'development',
      trace: options.devtoolsOptions?.trace ?? true,
    }) as StateCreator<T>;
  }

  return create<T>()(store);
};
