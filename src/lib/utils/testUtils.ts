import jwt from 'jsonwebtoken';

// Test data
export const testUser = {
    name: 'Test Admin',
    email: '<EMAIL>',
    phoneNumber: '1234567890',
    passwordHash: 'hashedPassword',
    isActive: true,
  };
  
 export const testPermissions = [
    { name: 'view_candidates' },
    { name: 'edit_candidates' },
    { name: 'delete_candidates' },
    { name: 'view_permissions' },
    { name: 'edit_permissions' },
  ];
  

  const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret';


export function createValidToken(permissions = ['view_permissions'], userId?: number) {
    return jwt.sign(
      {
        id: userId,
        name: testUser.name,
        email: testUser.email,
        roles: ['test_admin'],
        permissions,
      },
      JWT_SECRET,
      { expiresIn: '1h' }
    );
  }