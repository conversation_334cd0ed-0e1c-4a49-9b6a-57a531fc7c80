import { z } from 'zod';

export const BatchSchema = z.object({
  id: z.string().optional(),
  batchName: z.string().min(1),
  batchDate: z.coerce.date(),
  jobType: z.string().min(1),
  trainerId: z.string(),
  roomId: z.string(),
  candidateIds: z.array(z.string()).optional(),
  isLabBased: z.boolean(),
  isOjt: z.boolean(),
  remarks: z.string().optional(),
});

export type BatchInput = z.infer<typeof BatchSchema>; 