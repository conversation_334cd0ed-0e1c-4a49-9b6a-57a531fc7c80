'use client';

import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { authMeService } from '@/service/auth.service';
import { useAuthStore } from '@/store/authStore';
import type { AuthMe } from '@/types/user.type';

export const useAuth = () => {
  const { setUser, setLoading } = useAuthStore();

  const query = useQuery<AuthMe>({
    queryKey: ['me'],
    queryFn: authMeService,
    retry: false, // Don't retry on auth failures
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  useEffect(() => {
    if (query.isSuccess) {
      setUser(query.data);
    }
    if (query.isError) {
      setUser(null);
    }
    if (query.isSuccess || query.isError) {
      setLoading(false);
    }
  }, [query.isSuccess, query.isError, query.data, setUser, setLoading]);

  return query;
};
