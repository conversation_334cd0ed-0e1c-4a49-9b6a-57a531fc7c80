import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Schedule, DaySchedule, ModuleOption } from "@/components/template/ScheduleTable";

export interface TemplateFormValues {
  name: string;
  description: string;
  batchType: string;
  scheduleSA: Schedule;
  scheduleSM: Schedule;
  scheduleOPTOM: Schedule;
}

const batchTypes = [
  { value: "SA_SM", label: "SA_SM" },
  { value: "OPTOM", label: "OPTOM" },
];

const emptyDay: DaySchedule = { nature: "CRT", modules: [{ moduleId: 0, startTime: "", endTime: "" }] };
const emptySchedule: Schedule = Array.from({ length: 30 }, () => ({ ...emptyDay }));

function validateSchedule(schedule: Schedule, modules: ModuleOption[]) {
  const errors: Array<{ [key: string]: string }> = [];
  schedule.forEach((day, dayIdx) => {
    const dayErrors: { [key: string]: string } = {};
    if (!day.nature) {
      dayErrors.nature = "Nature is required";
    }
    if (day.nature === "CRT") {
      if (!day.modules.length) {
        dayErrors.modules = "At least one module is required";
      } else {
        day.modules.forEach((mod, modIdx) => {
          if (!mod.moduleId) {
            dayErrors[`module_${modIdx}`] = "Module is required";
          } else if (!modules.find((m) => m.id === mod.moduleId)) {
            dayErrors[`module_${modIdx}`] = "Invalid module";
          }
          if (!mod.startTime) {
            dayErrors[`startTime_${modIdx}`] = "Start time is required";
          }
          if (!mod.endTime) {
            dayErrors[`endTime_${modIdx}`] = "End time is required";
          }
          if (mod.startTime && mod.endTime && mod.endTime <= mod.startTime) {
            dayErrors[`endTime_${modIdx}`] = "End time must be after start time";
          }
        });
      }
    }
    errors.push(dayErrors);
  });
  return errors;
}

export function useCreateTemplateForm(initialValues?: Partial<TemplateFormValues>) {
  const router = useRouter();
  const [modules, setModules] = useState<ModuleOption[]>([]);
  const [name, setName] = useState(initialValues?.name || "");
  const [description, setDescription] = useState(initialValues?.description || "");
  const [batchType, setBatchType] = useState(initialValues?.batchType || "");
  const [scheduleSA, setScheduleSA] = useState<Schedule>(initialValues?.scheduleSA || emptySchedule);
  const [scheduleSM, setScheduleSM] = useState<Schedule>(initialValues?.scheduleSM || emptySchedule);
  const [scheduleOPTOM, setScheduleOPTOM] = useState<Schedule>(initialValues?.scheduleOPTOM || emptySchedule);
  const [nameTouched, setNameTouched] = useState(false);
  const [batchTypeTouched, setBatchTypeTouched] = useState(false);
  const [formError, setFormError] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const prevBatchType = useRef<string>("");

  // Update form state when initialValues change (for edit page)
  useEffect(() => {
    if (initialValues) {
      setName(initialValues.name || "");
      setDescription(initialValues.description || "");
      setBatchType(initialValues.batchType || "");
      setScheduleSA(initialValues.scheduleSA || emptySchedule);
      setScheduleSM(initialValues.scheduleSM || emptySchedule);
      setScheduleOPTOM(initialValues.scheduleOPTOM || emptySchedule);
      prevBatchType.current = initialValues.batchType || "";
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    initialValues?.name,
    initialValues?.description,
    initialValues?.batchType,
    initialValues?.scheduleSA,
    initialValues?.scheduleSM,
    initialValues?.scheduleOPTOM,
  ]);

  useEffect(() => {
    fetch("/api/modules?limit=100")
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          setModules(
            data.data.modules.map((m: any) => ({
              id: m.id,
              name: m.name,
              nature: m.nature,
              program: m.program,
            }))
          );
        }
      });
  }, []);

  // Only reset schedules if batchType is changed by user (not by initialValues)
  useEffect(() => {
    if (prevBatchType.current && batchType && prevBatchType.current !== batchType) {
      setScheduleSA(emptySchedule);
      setScheduleSM(emptySchedule);
      setScheduleOPTOM(emptySchedule);
    }
    prevBatchType.current = batchType;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchType]);

  const nameError = !name && nameTouched ? "Name is required" : "";
  const batchTypeError = !batchType && batchTypeTouched ? "Batch type is required" : "";

  const scheduleErrorsSA = batchType === "SA_SM" ? validateSchedule(scheduleSA, modules) : [];
  const scheduleErrorsSM = batchType === "SA_SM" ? validateSchedule(scheduleSM, modules) : [];
  const scheduleErrorsOPTOM = batchType === "OPTOM" ? validateSchedule(scheduleOPTOM, modules) : [];

  const hasScheduleErrors =
    (batchType === "SA_SM" && (scheduleErrorsSA.some((e) => Object.keys(e).length) || scheduleErrorsSM.some((e) => Object.keys(e).length))) ||
    (batchType === "OPTOM" && scheduleErrorsOPTOM.some((e) => Object.keys(e).length));

  const isFormValid = !nameError && !batchTypeError && !hasScheduleErrors;

  function buildSchedulePayload() {
    if (batchType === "SA_SM") {
      return [
        ...scheduleSA.map((day, idx) => ({ ...day, dayNumber: idx + 1, groupType: "SA" })),
        ...scheduleSM.map((day, idx) => ({ ...day, dayNumber: idx + 1, groupType: "SM" })),
      ];
    } else if (batchType === "OPTOM") {
      return scheduleOPTOM.map((day, idx) => ({ ...day, dayNumber: idx + 1, groupType: "ALL" }));
    }
    return [];
  }

  const handleSubmit = useCallback(async () => {
    setNameTouched(true);
    setBatchTypeTouched(true);
    setFormError("");
    if (!isFormValid) return false;
    setSubmitting(true);
    try {
      const res = await fetch("/api/templates", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          name,
          description,
          batchType,
          schedule: buildSchedulePayload(),
        }),
      });
      const data = await res.json();
      if (data.success) {
        toast.success("Template created successfully!");
        router.push("/templates");
        return true;
      } else {
        toast.error(data.error || "Failed to create template");
        return false;
      }
    } catch (err) {
      toast.error("Failed to create template");
      return false;
    } finally {
      setSubmitting(false);
    }
  }, [name, description, batchType, scheduleSA, scheduleSM, scheduleOPTOM, isFormValid, router]);

  return {
    name,
    setName,
    description,
    setDescription,
    batchType,
    setBatchType,
    scheduleSA,
    setScheduleSA,
    scheduleSM,
    setScheduleSM,
    scheduleOPTOM,
    setScheduleOPTOM,
    modules,
    batchTypes,
    nameError,
    batchTypeError,
    scheduleErrorsSA,
    scheduleErrorsSM,
    scheduleErrorsOPTOM,
    hasScheduleErrors,
    isFormValid,
    formError,
    setFormError,
    submitting,
    setNameTouched,
    setBatchTypeTouched,
    handleSubmit,
  };
} 