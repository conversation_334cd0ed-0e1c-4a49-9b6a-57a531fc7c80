import { useCallback, useEffect, useState } from "react";

export interface Template {
  id: number;
  name: string;
  batchType: string;
  version: number;
  isActive: boolean;
}

interface UseTemplatesResult {
  templates: Template[];
  loading: boolean;
  error: string | null;
  fetchTemplates: () => Promise<void>;
  deleteTemplate: (id: number) => Promise<{ success: boolean; error?: string }>;
}

export function useTemplates(): UseTemplatesResult {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch("/api/templates", { credentials: "include" });
      const data = await res.json();
      if (data.success) {
        setTemplates(data.data);
      } else {
        setError(data.error || "Failed to fetch templates");
      }
    } catch (err) {
      setError("Failed to fetch templates");
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteTemplate = useCallback(async (id: number) => {
    try {
      const res = await fetch(`/api/templates/${id}`, {
        method: "DELETE",
        credentials: "include",
      });
      const data = await res.json();
      if (!data.success) {
        return { success: false, error: data.error || "Failed to delete template" };
      }
      // Optionally, remove from local state for instant UI update
      setTemplates((prev) => prev.filter((t) => t.id !== id));
      return { success: true };
    } catch (e: any) {
      return { success: false, error: e.message || "Unknown error" };
    }
  }, []);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  return { templates, loading, error, fetchTemplates, deleteTemplate };
} 