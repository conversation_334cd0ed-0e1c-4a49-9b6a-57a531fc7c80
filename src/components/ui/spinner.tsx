'use client';

import React from 'react';

export default function Spinner({ size = 48, color = '#010042' }: { size?: number; color?: string }) {
  const bars = Array.from({ length: 12 });

  return (
    <div className="flex items-center justify-center h-screen">
      <div 
        className="relative"
        style={{ width: size, height: size }}
      >
        {bars.map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full spinner-bar"
            style={{
              width: size / 12,
              height: size / 4,
              left: size / 2 - size / 24,
              top: size / 24,
              transform: `rotate(${i * 30}deg)`,
              transformOrigin: `${size / 24}px ${size / 2 - size / 24}px`,
              backgroundColor: color,
              animationDelay: `${-((11 - i) * (1 / 12)).toFixed(3)}s`,
            }}
          />
        ))}

        <style jsx>{`
          .spinner-bar {
            animation: fadeSpin 1s linear infinite;
          }

          @keyframes fadeSpin {
            0% {
              opacity: 1;
            }
            100% {
              opacity: 0.15;
            }
          }
        `}</style>
      </div>
    </div>
  );
}