import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import type { Module } from "@/types/module";
import type { Pagination } from "@/types/location";
import { DataTable, DataTableColumn } from "@/components/common/DataTable";

interface ModuleTableProps {
  modules: Module[];
  loading?: boolean;
  error?: string | null;
  pagination: Pagination;
  onEdit: (module: Module) => void;
  onDelete: (module: Module) => void;
  onPageChange: (page: number) => void;
}

export function ModuleTable({
  modules,
  loading,
  error,
  pagination,
  onEdit,
  onDelete,
  onPageChange,
}: ModuleTableProps) {
  const columns: DataTableColumn<Module>[] = [
    { label: "Name", key: "name" },
    { label: "Description", key: "description" },
    { label: "Nature", key: "nature" },
    { label: "Program", key: "program" },
    {
      label: "Actions",
      render: (mod) => (
        <div className="flex gap-2">
          <Button
            className="cursor-pointer"
            size="sm"
            variant="outline"
            onClick={() => onEdit(mod)}
            aria-label={`Edit module ${mod.name}`}
          >
            Edit
          </Button>
          <Button
            className="cursor-pointer"
            size="sm"
            variant="destructive"
            onClick={() => onDelete(mod)}
            aria-label={`Delete module ${mod.name}`}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={modules}
      loading={loading}
      error={error}
      pagination={pagination}
      onPageChange={onPageChange}
    />
  );
}
