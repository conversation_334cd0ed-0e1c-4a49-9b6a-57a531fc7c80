import React from "react";
import type { ModuleForm, ModuleNature, ProgramType } from "@/types/module";
import { Form, FormFieldDef } from "@/components/common/Form";

const NATURES: { label: string; value: ModuleNature }[] = [
  { label: "Classroom Training (CRT)", value: "CRT" },
  { label: "On-the-Job Training (OJT)", value: "OJT" },
];

const PROGRAMS: { label: string; value: ProgramType }[] = [
  { label: "SA", value: "SA" },
  { label: "SM", value: "SM" },
  { label: "Optom", value: "OPTOM" },
];

interface ModuleFormProps {
  initialValues?: ModuleForm;
  onSubmit: (values: ModuleForm) => void;
  onCancel?: () => void;
  loading?: boolean;
  error?: string | null;
}

const defaultForm: ModuleForm = {
  name: "",
  description: "",
  duration: 1,
  nature: "CRT",
  program: "SA",
};

export function ModuleFormComponent({ initialValues, onSubmit, onCancel, loading, error }: ModuleFormProps) {
  const fields: FormFieldDef<ModuleForm>[] = [
    { label: "Name", name: "name", type: "text", required: true, inputProps: { maxLength: 64 } },
    { label: "Description", name: "description", type: "text", inputProps: { maxLength: 256 } },
    { label: "Nature", name: "nature", type: "select", required: true, options: NATURES },
    { label: "Program", name: "program", type: "select", required: true, options: PROGRAMS },
  ];
  return (
    <Form
      fields={fields}
      initialValues={initialValues || defaultForm}
      onSubmit={onSubmit}
      onCancel={onCancel}
      loading={loading}
      error={error}
    />
  );
} 