import React from "react";
import { use<PERSON><PERSON>, SubmitHandler, Control } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form as ShadcnForm,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export type FieldType = "text" | "number" | "select" | "textarea" | "custom";

export interface FormFieldDef<T> {
  label: string;
  name: keyof T;
  type: FieldType;
  required?: boolean;
  options?: { label: string; value: any }[]; // for select
  render?: (fieldProps: any, form: any, controlProps?: Record<string, any>) => React.ReactNode; // for custom
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  textareaProps?: React.TextareaHTMLAttributes<HTMLTextAreaElement>;
  className?: string;
}

interface FormProps<T extends Record<string, any>> {
  fields: FormFieldDef<T>[];
  initialValues: T;
  onSubmit: SubmitHandler<T>;
  onCancel?: () => void;
  loading?: boolean;
  error?: string | null;
  submitLabel?: string;
  cancelLabel?: string;
}

export function Form<T extends Record<string, any>>({
  fields,
  initialValues,
  onSubmit,
  onCancel,
  loading,
  error,
  submitLabel = "Save",
  cancelLabel = "Cancel",
}: FormProps<T>) {
  // We use 'any' here to avoid react-hook-form generic type issues with dynamic forms
  const form = useForm({
    defaultValues: initialValues as any,
    mode: "onBlur",
  });

  const { handleSubmit, control } = form;

  return (
    <ShadcnForm {...form}>
      <form
        onSubmit={handleSubmit(onSubmit as any)}
        className="flex flex-col gap-4 mt-4 p-4 max-w-md w-full mx-auto"
        autoComplete="off"
      >
        {fields.map((field) => (
          <FormField
            key={String(field.name)}
            control={control as unknown as Control<any>}
            name={field.name as any}
            rules={{ required: field.required ? `${field.label} is required.` : false }}
            render={({ field: rhfField }) => (
              <FormItem>
                <FormLabel>{field.label}</FormLabel>
                <FormControl>
                  {field.type === "text" ? (
                    <Input {...rhfField} {...field.inputProps} />
                  ) : field.type === "number" ? (
                    <Input type="number" {...rhfField} {...field.inputProps} />
                  ) : field.type === "textarea" ? (
                    <textarea className="w-full rounded border px-3 py-2" {...rhfField} {...field.textareaProps} />
                  ) : field.type === "select" && field.options ? (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button type="button" variant="outline" className="w-full justify-between">
                          {field.options.find((o) => o.value === rhfField.value)?.label || `Select ${field.label}`}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start" className="w-full">
                        {field.options.map((o) => (
                          <DropdownMenuItem key={o.value} onSelect={() => rhfField.onChange(o.value)}>{o.label}</DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : field.type === "custom" && field.render ? (
                    // Pass controlProps to custom renderers so they can spread them on the root element
                    field.render(rhfField, form, arguments[0]?.field)
                  ) : null}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ))}
        {error && <div className="text-destructive text-sm">{error}</div>}
        <div className="flex gap-2 mt-2">
          <Button type="submit" disabled={loading}>{loading ? "Saving..." : submitLabel}</Button>
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>{cancelLabel}</Button>
          )}
        </div>
      </form>
    </ShadcnForm>
  );
} 