import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import type { Pagination } from "@/types/location";

export interface DataTableColumn<T> {
  label: string;
  key?: keyof T;
  render?: (row: T) => React.ReactNode;
  className?: string;
}

interface DataTableProps<T> {
  columns: DataTableColumn<T>[];
  data: T[];
  loading?: boolean;
  error?: string | null;
  pagination: Pagination;
  onPageChange: (page: number) => void;
}

export function DataTable<T extends { id: number }>({
  columns,
  data,
  loading,
  error,
  pagination,
  onPageChange,
}: DataTableProps<T>) {
  return (
    <div className="w-full overflow-x-auto p-2 sm:p-4">
      {error && <div className="text-destructive mb-2">{error}</div>}
      <Table className="min-w-full w-full">
        <TableHeader>
          <TableRow>
            {columns.map((col) => (
              <TableHead
                key={col.label}
                className={col.className || "px-2 py-2"}
              >
                {col.label}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={columns.length} className="text-center py-8">
                Loading...
              </TableCell>
            </TableRow>
          ) : data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={columns.length} className="text-center py-8">
                No data found.
              </TableCell>
            </TableRow>
          ) : (
            data.map((row) => (
              <TableRow key={row.id}>
                {columns.map((col, idx) => (
                  <TableCell
                    key={col.label + idx}
                    className={col.className || "px-2 py-2"}
                  >
                    {col.render
                      ? col.render(row)
                      : col.key
                        ? (row[col.key] as React.ReactNode)
                        : null}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      {pagination.totalPages > 1 && (
        <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-2">
          <div className="text-sm text-muted-foreground">
            Page {pagination.page} of {pagination.totalPages} (
            {pagination.total} total)
          </div>
          <div className="flex gap-2 flex-wrap">
            <Button
              size="sm"
              variant="outline"
              onClick={() => onPageChange(1)}
              disabled={pagination.page === 1}
            >
              First
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={!pagination.hasPrev}
            >
              Prev
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={!pagination.hasNext}
            >
              Next
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onPageChange(pagination.totalPages)}
              disabled={pagination.page === pagination.totalPages}
            >
              Last
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
