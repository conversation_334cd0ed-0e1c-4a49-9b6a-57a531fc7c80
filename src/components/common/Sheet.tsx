import React from "react";
import {
  Sheet as <PERSON><PERSON>hee<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Des<PERSON>,
  Sheet<PERSON>ooter,
  SheetClose,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

interface SheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  onClose?: () => void;
  children: React.ReactNode;
  side?: "right" | "left" | "top" | "bottom";
  widthClass?: string; // e.g. 'max-w-md w-full'
  footer?: React.ReactNode;
}

export function Sheet({
  open,
  onOpenChange,
  title,
  description,
  onClose,
  children,
  side = "right",
  widthClass = "max-w-md w-full",
  footer,
}: SheetProps) {
  return (
    <UISheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side={side} className={widthClass}>
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          {description && <SheetDescription>{description}</SheetDescription>}
        </SheetHeader>
        {children}
        <SheetFooter>
          {footer ? (
            footer
          ) : (
            <SheetClose asChild>
              <Button className="cursor-pointer" type="button" variant="outline" onClick={onClose}>
                Close
              </Button>
            </SheetClose>
          )}
        </SheetFooter>
      </SheetContent>
    </UISheet>
  );
} 