import Image from 'next/image';
import Link from 'next/link';
import type * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar';

// This is sample data.
const data = {
  navMain: [
    { title: 'Dashboard', url: '/dashboard' },
    { title: 'Roasters', url: '/roasters' },
    { title: 'Trainers', url: '/trainers' },
    { title: 'Locations', url: '/locations' },
    { title: 'Holidays', url: '/holidays' },
    { title: 'Modules', url: '/modules' },
    { title: 'Templates', url: '/templates' },
    { title: 'Batches', url: '/batches' },
    { title: 'Schedules', url: '/schedules' },
    // { title: 'Academy', url: '/academy' },
    { title: 'Talent Acquisition', url: '/talent-acquisition' },
    // { title: 'Roles Management', url: '/roles-management' },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <Image
          alt="Lenskart Logo"
          height={80}
          src="/lenskartLogo.png"
          width={100}
        />
      </SidebarHeader>
      <SidebarContent className="gap-0">
        {data.navMain.map((item) => (
          <SidebarGroup key={item.title}>
            <SidebarGroupLabel
              asChild
              className="cursor-pointer text-sidebar-foreground text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
            >
              <Link href={item.url}>{item.title}</Link>
            </SidebarGroupLabel>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
