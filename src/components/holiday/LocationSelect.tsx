import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import type { Location } from "@/types/location";

interface LocationSelectProps {
  value: number | null;
  onChange: (value: number) => void;
  locations: Location[];
  disabled?: boolean;
  placeholder?: string;
}

export function LocationSelect({ value, onChange, locations, disabled, placeholder }: LocationSelectProps) {
  const selected = locations.find((l) => l.id === value);
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button type="button" variant="outline" className="w-full justify-between" disabled={disabled} aria-label="Select location">
          {selected ? selected.name : (placeholder || "Select location")}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-full max-h-60 overflow-y-auto">
        {locations.map((loc) => (
          <DropdownMenuItem key={loc.id} onSelect={() => onChange(loc.id)}>{loc.name}</DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 