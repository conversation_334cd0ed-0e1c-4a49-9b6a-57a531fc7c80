import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormFieldDef } from "@/components/common/Form";
import { LocationSelect } from "@/components/holiday/LocationSelect";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, isBefore, startOfDay } from "date-fns";
import type { HolidayForm } from "@/types/holiday";
import type { Location } from "@/types/location";

interface HolidayFormProps {
  initialValues?: HolidayForm;
  onSubmit: (values: HolidayForm) => void;
  onCancel?: () => void;
  loading?: boolean;
  error?: string | null;
  locations: Location[];
}

const defaultForm: HolidayForm = {
  date: "",
  reason: "",
  locationId: 0,
};

export function HolidayForm({ initialValues, onSubmit, onCancel, loading, error, locations }: HolidayFormProps) {
  const fields: FormFieldDef<HolidayForm>[] = [
    {
      label: "Date",
      name: "date",
      type: "custom",
      required: true,
      render: (field, form, controlProps) => (
        <Popover {...controlProps}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={
                "w-full justify-start text-left font-normal " +
                (!field.value ? "text-muted-foreground" : "")
              }
              type="button"
            >
              {field.value && !isNaN(Date.parse(field.value))
                ? format(new Date(field.value), "yyyy-MM-dd")
                : "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={field.value && !isNaN(Date.parse(field.value)) ? new Date(field.value) : undefined}
              onSelect={(date) => {
                if (date && !isNaN(date.getTime())) {
                  form.setValue("date", format(date, "yyyy-MM-dd"), { shouldValidate: true });
                }
              }}
              disabled={(date) => isBefore(startOfDay(date), startOfDay(new Date()))}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      label: "Description",
      name: "reason",
      type: "text",
      required: true,
      inputProps: { maxLength: 128 },
    },
    {
      label: "Location",
      name: "locationId",
      type: "custom",
      required: true,
      render: (field, _form, controlProps) => (
        <div {...controlProps}>
          <LocationSelect value={field.value || null} onChange={field.onChange} locations={locations} />
        </div>
      ),
    },
  ];

  return (
    <Form
      fields={fields}
      initialValues={initialValues || defaultForm}
      onSubmit={onSubmit}
      onCancel={onCancel}
      loading={loading}
      error={error}
    />
  );
} 