import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import type { Holiday } from "@/types/holiday";
import type { Pagination } from "@/types/location";
import { DataTable, DataTableColumn } from "../common/DataTable";

interface HolidayTableProps {
  holidays: Holiday[];
  loading?: boolean;
  error?: string | null;
  pagination: Pagination;
  onEdit: (holiday: Holiday) => void;
  onDelete: (holiday: Holiday) => void;
  onPageChange: (page: number) => void;
}

export function HolidayTable({
  holidays,
  loading,
  error,
  pagination,
  onEdit,
  onDelete,
  onPageChange,
}: HolidayTableProps) {
  const columns: DataTableColumn<Holiday>[] = [
    { label: "Date", key: "date", render: (row) => row.date.slice(0, 10) },
    { label: "Description", key: "reason" },
    {
      label: "Location",
      key: "location",
      render: ({ location }) => <div className="px-2 py-2">{location?.name}</div>,
    },
    {
      label: "Actions",
      render: (data) => (
        <div className="flex gap-2 px-2 py-2">
          <Button
            className="cursor-pointer"
            size="sm"
            variant="outline"
            onClick={() => onEdit(data)}
            aria-label={`Edit holiday on ${data.date}`}
          >
            Edit
          </Button>
          <Button
            className="cursor-pointer"
            size="sm"
            variant="destructive"
            onClick={() => onDelete(data)}
            aria-label={`Delete holiday on ${data.date}`}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={holidays}
      loading={loading}
      error={error}
      pagination={pagination}
      onPageChange={onPageChange}
    />
  );
}
