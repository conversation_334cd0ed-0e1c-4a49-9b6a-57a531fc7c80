import React from "react";
import { Button } from "@/components/ui/button";
import type { Location, Pagination } from "@/types/location";
import { DataTable, DataTableColumn } from "../common/DataTable";

interface LocationTableProps {
  locations: Location[];
  loading?: boolean;
  error?: string | null;
  pagination: Pagination;
  onEdit: (location: Location) => void;
  onDelete: (location: Location) => void;
  onPageChange: (page: number) => void;
}

const LOCATION_TYPES = [
  { label: "Academy", value: "ACADEMY" },
  { label: "Satellite", value: "SATELLITE" },
];

export function LocationTable({
  locations,
  loading,
  error,
  pagination,
  onEdit,
  onDelete,
  onPageChange,
}: LocationTableProps) {
  const columns: DataTableColumn<Location>[] = [
    { label: "Name", key: "name" },
    { label: "Circle", key: "circle" },
    { label: "Address", key: "address" },
    {
      label: "Type",
      render: (data) => (
        <div className="px-2 py-2">
          {LOCATION_TYPES.find((t) => t.value === data.type)?.label || data.type}
        </div>
      ),
    },
    {
      label: "Actions",
      render: (data) => (
        <div className="flex gap-2 px-2 py-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onEdit(data)}
            aria-label={`Edit ${data.name}`}
          >
            Edit
          </Button>
          <Button
            size="sm"
            variant="destructive"
            onClick={() => onDelete(data)}
            aria-label={`Delete ${data.name}`}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];
  return (
    <DataTable
      columns={columns}
      data={locations}
      loading={loading}
      error={error}
      pagination={pagination}
      onPageChange={onPageChange}
    />
  );
}
