import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormFieldDef } from "@/components/common/Form";
import { LocationTypeSelect } from "@/components/location/LocationTypeSelect";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Location, LocationType } from "@/types/location";

interface LocationFormProps {
  initialValues?: Omit<Location, "id">;
  onSubmit: (values: Omit<Location, "id">) => void;
  onCancel?: () => void;
  loading?: boolean;
  error?: string | null;
}

const defaultForm: Omit<Location, "id"> = {
  name: "",
  circle: "",
  address: "",
  type: "ACADEMY",
};

const REGION_OPTIONS = [
  { value: "North", label: "North" },
  { value: "South", label: "South" },
  { value: "West", label: "West" },
  { value: "East", label: "East" },
];

export function LocationForm({
  initialValues,
  onSubmit,
  onCancel,
  loading,
  error,
}: LocationFormProps) {
  const fields: FormFieldDef<Omit<Location, "id">>[] = [
    {
      label: "Name",
      name: "name",
      type: "text",
      required: true,
      inputProps: { maxLength: 32, autoFocus: true },
    },
    {
      label: "Region",
      name: "circle",
      type: "custom",
      required: true,
      render: (field, _form, controlProps) => (
        <div {...controlProps}>
          <Select value={field.value || ""} onValueChange={field.onChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select region" />
            </SelectTrigger>
            <SelectContent>
              {REGION_OPTIONS.map((opt) => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      ),
    },
    {
      label: "Address",
      name: "address",
      type: "text",
      required: true,
      inputProps: { maxLength: 128 },
    },
    {
      label: "Type",
      name: "type",
      type: "custom",
      required: true,
      render: (field, _form, controlProps) => (
        <div {...controlProps}>
          <LocationTypeSelect
            value={field.value as LocationType}
            onChange={field.onChange}
          />
        </div>
      ),
    },
  ];
  return (
    <Form
      fields={fields}
      initialValues={initialValues || defaultForm}
      onSubmit={onSubmit}
      onCancel={onCancel}
      loading={loading}
      error={error}
    />
  );
}
