import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import type { LocationType } from "@/types/location";

const LOCATION_TYPES = [
  { label: "Academy", value: "ACADEMY" },
  { label: "Satellite", value: "SATELLITE" },
];

interface LocationTypeSelectProps {
  value: LocationType;
  onChange: (value: LocationType) => void;
  disabled?: boolean;
}

export function LocationTypeSelect({ value, onChange, disabled }: LocationTypeSelectProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button type="button" variant="outline" className="w-full justify-between" disabled={disabled} aria-label="Select location type">
          {LOCATION_TYPES.find((t) => t.value === value)?.label || "Select type"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-full">
        {LOCATION_TYPES.map((t) => (
          <DropdownMenuItem key={t.value} onSelect={() => onChange(t.value as LocationType)}>{t.label}</DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 