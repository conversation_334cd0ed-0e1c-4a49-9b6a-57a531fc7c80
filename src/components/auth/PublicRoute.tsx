'use client';

import { useRouter } from 'next/navigation';
import { type ReactNode, useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import Spinner from '../ui/spinner';

interface PublicRouteProps {
  children: ReactNode;
  redirectTo?: string;
  redirectIfAuthenticated?: boolean;
}

const PublicRoute = ({
  children,
  redirectTo = '/dashboard',
  redirectIfAuthenticated = false,
}: PublicRouteProps) => {
  const { isAuthenticated, isLoading } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    // Only redirect if we're not loading, user is authenticated, and redirect is enabled
    if (!isLoading && isAuthenticated && redirectIfAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo, redirectIfAuthenticated]);

  // Show loading state while checking authentication
  if (isLoading) {
    return <Spinner />;
  }

  // If authenticated and should redirect, show nothing (redirect will happen via useEffect)
  if (isAuthenticated && redirectIfAuthenticated) {
    return null;
  }

  // Render the public content
  return <>{children}</>;
};

export default PublicRoute;
