'use client';

import { useMutation } from '@tanstack/react-query';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { authMeService, loginService } from '@/service/auth.service';
import { useAuthStore } from '@/store/authStore';
import type { loginType } from '@/types/login.type';

import LKLogo from '../../public/lenskartLogo.png';
import LKAcademyLogo from '../../public/lkAcademyLogo.webp';

// Define regex at the top level
const EMAIL_REGEX = /\S+@\S+\.\S+/;

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<'div'>) {
  const [loginForm, setLoginForm] = useState<loginType>({
    email: '',
    password: '',
  });
  const Router = useRouter();

  const setUser = useAuthStore((state) => state.setUser);

  const mutation = useMutation({
    mutationFn: loginService,
    onSuccess: async (data) => {
      if (data.success) {
        const authMeResponse = await authMeService();
        if (authMeResponse) {
          setUser(authMeResponse);
          toast.success('Login successful!');
          Router.replace('/dashboard'); // User can't go back to login
        }
      }
    },
    onError: (error) => {
      // Handle error case, e.g., show a toast notification
      toast.error(error.message || 'Login failed. Please try again.');
    },
  });

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setLoginForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (event: React.FormEvent<HTMLButtonElement>) => {
    event.preventDefault();
    if (!(loginForm.email && loginForm.password)) {
      toast.error('Please fill in all fields.');
      return;
    }
    if (!EMAIL_REGEX.test(loginForm.email)) {
      toast.error('Please enter a valid email address.');
      return;
    }
    // Handle form submission logic here
    mutation.mutate({
      email: loginForm.email,
      password: loginForm.password,
    });
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card className="overflow-hidden p-0">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <Image
                  alt="Lenskart Logo"
                  height={103}
                  src={LKLogo}
                  width={125}
                />
                <h1 className="font-bold text-2xl">Welcome back</h1>

                <p className="text-balance text-muted-foreground">
                  Login to your LK Academy account
                </p>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  required
                  type="email"
                  value={loginForm.email}
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  onChange={handleChange}
                  required
                  type="password"
                  value={loginForm.password}
                />
              </div>
              <Button
                className="w-full cursor-pointer"
                disabled={mutation.isPending}
                onClick={handleSubmit}
                type="submit"
              >
                Login
              </Button>
            </div>
            <div className="my-2 flex items-center">
              <Link
                className="ml-auto text-sm underline-offset-2 hover:underline"
                href="/forgot-password"
              >
                Forgot your password?
              </Link>
            </div>
          </form>
          <div className="relative hidden bg-muted md:block">
            <Image
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
              height={480}
              src={LKAcademyLogo}
              width={480}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
