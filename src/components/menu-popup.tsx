'use client';

import { useMutation } from '@tanstack/react-query';
import { CircleUser, LogOut } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { logoutService } from '@/service/auth.service';
import { useAuthStore } from '@/store/authStore';

export default function UserMenu({ className }: { className: string }) {
  const [open, setOpen] = useState(false);
  const Router = useRouter();

  const logout = useAuthStore((state) => state.logout);

  const { mutate } = useMutation({
    mutationFn: logoutService,
    onSuccess: (data) => {
      if (data.success) {
        logout(); // clears Zustand user\
        toast.success('Logout successful!');
        Router.replace('/login'); // no need to revalidate /me
      }
    },
    onError: (error) => {
      toast.error(error.message || 'Logout failed. Please try again.');
    },
  });

  const handleLogout = () => {
    mutate();
  };

  return (
    <div className={className}>
      <Popover onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild>
          <div className="absolute right-0 bottom-[-5px] cursor-pointer rounded-full p-2 hover:bg-accent">
            <CircleUser size={24} />
          </div>
        </PopoverTrigger>
        <PopoverContent className="mr-2 w-40 space-y-1 p-2">
          <Link
            className="block w-full rounded px-2 py-1 text-sm hover:bg-muted"
            href="/profile"
          >
            Profile
          </Link>
          <button
            className="flex w-full cursor-pointer items-center gap-2 rounded px-2 py-1 text-left text-destructive text-sm hover:bg-muted"
            onClick={handleLogout}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleLogout();
              }
            }}
            type="button"
          >
            Logout
            <LogOut className="h-3.5 w-3.5" />
          </button>
        </PopoverContent>
      </Popover>
    </div>
  );
}
