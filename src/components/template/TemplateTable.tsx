import { DataTable, DataTableColumn } from "@/components/common/DataTable";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Template } from "@/hooks/useTemplates";
import type { Pagination } from "@/types/location";

interface TemplateTableProps {
  templates: Template[];
  loading?: boolean;
  error?: string | null;
  pagination: Pagination;
  onEdit: (template: Template) => void;
  onDelete: (template: Template) => void;
  onPageChange: (page: number) => void;
}

export function TemplateTable({
  templates,
  loading,
  error,
  pagination,
  onEdit,
  onDelete,
  onPageChange,
}: TemplateTableProps) {
  const columns: DataTableColumn<Template>[] = [
    { label: "Name", key: "name" },
    { label: "Batch Type", key: "batchType" },
    { label: "Version", key: "version" },
    {
      label: "Actions",
      render: (template) => (
        <div className="flex gap-2 justify-end">
          <Link href={`/templates/${template.id}/edit`}>
            <Button
              size="sm"
              variant="outline"
              type="button"
              onClick={() => onEdit(template)}
            >
              Edit
            </Button>
          </Link>
          <Button
            size="sm"
            variant="destructive"
            type="button"
            onClick={() => onDelete(template)}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];
  return (
    <DataTable
      columns={columns}
      data={templates}
      loading={loading}
      error={error}
      pagination={pagination}
      onPageChange={onPageChange}
    />
  );
}
