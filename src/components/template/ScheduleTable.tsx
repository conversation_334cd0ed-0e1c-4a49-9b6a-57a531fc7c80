import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TimePicker } from "./TimePicker";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PlusIcon, Trash2Icon } from "lucide-react";

export type ModuleOption = {
  id: number;
  name: string;
  nature: "CRT" | "OJT";
  program: string;
};

export type DayModule = {
  moduleId: number;
  startTime: string;
  endTime: string;
};

export type DaySchedule = {
  nature: "CRT" | "OJT";
  modules: DayModule[];
};

export type Schedule = DaySchedule[];

interface ScheduleTableProps {
  program: "SA" | "SM" | "OPTOM";
  modules: ModuleOption[];
  value: Schedule;
  onChange: (schedule: Schedule) => void;
  errors?: Array<{ [key: string]: string }>;
}

const NATURES = [
  { label: "Classroom Training (CRT)", value: "CRT" },
  { label: "On-the-Job Training (OJT)", value: "OJT" },
];

function getDuration(start: string, end: string): number {
  if (!start || !end) return 0;
  const [sh, sm] = start.split(":").map(Number);
  const [eh, em] = end.split(":").map(Number);
  return (eh * 60 + em - (sh * 60 + sm)) / 60;
}

export function ScheduleTable({ program, modules, value, onChange, errors }: ScheduleTableProps) {
  // Filter modules for this program
  const filteredModules = modules.filter((m) => m.program === program && m.nature === "CRT");

  const handleNatureChange = (dayIdx: number, nature: "CRT" | "OJT") => {
    const updated = value.map((d, i) =>
      i === dayIdx
        ? { nature, modules: nature === "CRT" ? (d.modules.length ? d.modules : [{ moduleId: 0, startTime: "", endTime: "" }]) : [] }
        : d
    );
    onChange(updated);
  };

  const handleModuleChange = (dayIdx: number, modIdx: number, field: keyof DayModule, val: any) => {
    const updated = value.map((d, i) => {
      if (i !== dayIdx) return d;
      const modules = d.modules.map((m, j) => (j === modIdx ? { ...m, [field]: val } : m));
      return { ...d, modules };
    });
    onChange(updated);
  };

  const handleAddModule = (dayIdx: number) => {
    const updated = value.map((d, i) =>
      i === dayIdx ? { ...d, modules: [...d.modules, { moduleId: 0, startTime: "", endTime: "" }] } : d
    );
    onChange(updated);
  };

  const handleRemoveModule = (dayIdx: number, modIdx: number) => {
    const updated = value.map((d, i) => {
      if (i !== dayIdx) return d;
      const modules = d.modules.filter((_, j) => j !== modIdx);
      return { ...d, modules: modules.length ? modules : [{ moduleId: 0, startTime: "", endTime: "" }] };
    });
    onChange(updated);
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-16">Day</TableHead>
            <TableHead>Nature</TableHead>
            <TableHead>Modules (CRT only)</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {value.map((day, dayIdx) => {
            return (
              <TableRow key={dayIdx}>
                <TableCell className="font-semibold">Day {dayIdx + 1}</TableCell>
                <TableCell>
                  <Select value={day.nature} onValueChange={(val) => handleNatureChange(dayIdx, val as "CRT" | "OJT") }>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Select nature" />
                    </SelectTrigger>
                    <SelectContent>
                      {NATURES.map((n) => (
                        <SelectItem key={n.value} value={n.value}>{n.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors && errors[dayIdx]?.nature && (
                    <div className="text-destructive text-xs mt-1">{errors[dayIdx].nature}</div>
                  )}
                </TableCell>
                <TableCell>
                  {day.nature === "OJT" ? (
                    <span className="italic text-muted-foreground">Full day OJT</span>
                  ) : (
                    <div className="flex flex-col gap-2">
                      {errors && errors[dayIdx]?.modules && (
                        <div className="text-destructive text-xs mb-1">{errors[dayIdx].modules}</div>
                      )}
                      {day.modules.map((mod, modIdx) => (
                        <div key={modIdx} className="grid grid-cols-[minmax(120px,1fr)_minmax(100px,0.7fr)_minmax(100px,0.7fr)_auto_auto] gap-x-2 gap-y-0 items-center mb-2 min-w-[600px]">
                          <div className="flex flex-col justify-center">
                            <Select
                              value={mod.moduleId ? String(mod.moduleId) : ""}
                              onValueChange={(val) => handleModuleChange(dayIdx, modIdx, "moduleId", Number(val))}
                            >
                              <SelectTrigger className="w-48">
                                <SelectValue placeholder="Select module" />
                              </SelectTrigger>
                              <SelectContent>
                                {filteredModules.map((m) => (
                                  <SelectItem key={m.id} value={String(m.id)}>{m.name}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {errors && errors[dayIdx]?.[`module_${modIdx}`] && (
                              <div className="text-destructive text-xs mt-1">{errors[dayIdx][`module_${modIdx}`]}</div>
                            )}
                          </div>
                          <div className="flex flex-col justify-center min-w-[100px]">
                            <TimePicker
                              value={mod.startTime}
                              onChange={(val) => handleModuleChange(dayIdx, modIdx, "startTime", val)}
                              label="Start time"
                              buttonClassName="w-35 whitespace-nowrap"
                            />
                            {errors && errors[dayIdx]?.[`startTime_${modIdx}`] && (
                              <div className="text-destructive text-xs mt-1">{errors[dayIdx][`startTime_${modIdx}`]}</div>
                            )}
                          </div>
                          <div className="flex flex-col justify-center min-w-[100px]">
                            <TimePicker
                              value={mod.endTime}
                              onChange={(val) => handleModuleChange(dayIdx, modIdx, "endTime", val)}
                              label="End time"
                              buttonClassName="w-35 whitespace-nowrap"
                            />
                            {errors && errors[dayIdx]?.[`endTime_${modIdx}`] && (
                              <div className="text-destructive text-xs mt-1">{errors[dayIdx][`endTime_${modIdx}`]}</div>
                            )}
                          </div>
                          <div className="flex items-center justify-center">
                            <Button
                              type="button"
                              size="icon"
                              variant="ghost"
                              onClick={() => handleRemoveModule(dayIdx, modIdx)}
                              aria-label="Remove module"
                              className="text-destructive"
                              disabled={day.modules.length === 1}
                            >
                              <Trash2Icon className="w-4 h-4" />
                            </Button>
                          </div>
                          <div className="flex items-center justify-center">
                            {modIdx === day.modules.length - 1 && (
                              <Button
                                type="button"
                                size="icon"
                                variant="outline"
                                onClick={() => handleAddModule(dayIdx)}
                                aria-label="Add module"
                              >
                                <PlusIcon className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
} 