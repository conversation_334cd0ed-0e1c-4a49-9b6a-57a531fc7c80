import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface TimePickerProps {
  value: string;
  onChange: (val: string) => void;
  label?: string;
  buttonClassName?: string;
}

export function TimePicker({ value, onChange, label, buttonClassName }: TimePickerProps) {
  const [open, setOpen] = useState(false);

  // Generate time options in 15-min increments
  const times: string[] = [];
  for (let h = 9; h < 19; h++) {
    for (let m = 0; m < 60; m += 15) {
      times.push(`${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}`);
    }
  }

  return (
    <div>
      {label && <label className="block text-xs mb-1">{label}</label>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant="outline"
            className={buttonClassName || "w-32"}
            aria-label={label || "Pick time"}
          >
            {value || "Select time"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 max-h-60 overflow-y-auto w-32">
          <ul>
            {times.map((t) => (
              <li key={t}>
                <button
                  type="button"
                  className={`w-full text-left px-2 py-1 hover:bg-accent ${value === t ? "bg-accent" : ""}`}
                  onClick={() => {
                    onChange(t);
                    setOpen(false);
                  }}
                >
                  {t}
                </button>
              </li>
            ))}
          </ul>
        </PopoverContent>
      </Popover>
    </div>
  );
} 