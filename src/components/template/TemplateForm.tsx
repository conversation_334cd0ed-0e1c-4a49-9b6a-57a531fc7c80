import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Link from "next/link";
import { ScheduleTable } from "@/components/template/ScheduleTable";
import { ModuleOption, Schedule } from "@/components/template/ScheduleTable";

interface TemplateFormProps {
  name: string;
  setName: (v: string) => void;
  description: string;
  setDescription: (v: string) => void;
  batchType: string;
  setBatchType: (v: string) => void;
  scheduleSA: Schedule;
  setScheduleSA: (s: Schedule) => void;
  scheduleSM: Schedule;
  setScheduleSM: (s: Schedule) => void;
  scheduleOPTOM: Schedule;
  setScheduleOPTOM: (s: Schedule) => void;
  modules: ModuleOption[];
  batchTypes: { value: string; label: string }[];
  nameError: string;
  batchTypeError: string;
  scheduleErrorsSA: any[];
  scheduleErrorsSM: any[];
  scheduleErrorsOPTOM: any[];
  hasScheduleErrors: boolean;
  isFormValid: boolean;
  formError: string;
  setFormError: (v: string) => void;
  submitting: boolean;
  setNameTouched: (v: boolean) => void;
  setBatchTypeTouched: (v: boolean) => void;
  onSubmit: () => Promise<void | boolean>;
  submitText?: string;
  cancelHref?: string;
}

export function TemplateForm({
  name,
  setName,
  description,
  setDescription,
  batchType,
  setBatchType,
  scheduleSA,
  setScheduleSA,
  scheduleSM,
  setScheduleSM,
  scheduleOPTOM,
  setScheduleOPTOM,
  modules,
  batchTypes,
  nameError,
  batchTypeError,
  scheduleErrorsSA,
  scheduleErrorsSM,
  scheduleErrorsOPTOM,
  hasScheduleErrors,
  isFormValid,
  formError,
  setFormError,
  submitting,
  setNameTouched,
  setBatchTypeTouched,
  onSubmit,
  submitText = "Save",
  cancelHref = "/templates",
}: TemplateFormProps) {
  // Debug log for batchType and batchTypes
  console.log("[TemplateForm] batchType:", batchType, "batchTypes:", batchTypes);

  return (
    <form
      className="space-y-6"
      onSubmit={async e => {
        e.preventDefault();
        setFormError("");
        await onSubmit();
      }}
    >
      <div>
        <Label htmlFor="name">Name</Label>
        <Input id="name" name="name" required className="mt-1" value={name} onChange={e => setName(e.target.value)} onBlur={() => setNameTouched(true)} />
        {nameError && <div className="text-destructive text-sm mt-1">{nameError}</div>}
      </div>
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea id="description" name="description" className="mt-1" value={description} onChange={e => setDescription(e.target.value)} />
      </div>
      <div>
        <Label htmlFor="batchType">Batch Type</Label>
        <Select
          key={batchType || "empty"}
          value={batchType}
          onValueChange={val => { setBatchType(val); setBatchTypeTouched(true); }}
        >
          <SelectTrigger id="batchType" className="mt-1">
            <SelectValue placeholder="Select batch type" />
          </SelectTrigger>
          <SelectContent>
            {batchTypes.map((type) => (
              <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        {batchTypeError && <div className="text-destructive text-sm mt-1">{batchTypeError}</div>}
      </div>
      {/* Schedule Table */}
      {batchType && (
        <div className="mt-8">
          <h2 className="text-lg font-semibold mb-2">Schedule (30 Days)</h2>
          {batchType === "SA_SM" ? (
            <div className="flex flex-col md:flex-row gap-8">
              <div className="flex-1 overflow-x-auto w-full min-w-0">
                <h3 className="font-semibold mb-2">SA</h3>
                <ScheduleTable
                  program="SA"
                  modules={modules}
                  value={scheduleSA}
                  onChange={setScheduleSA}
                  errors={scheduleErrorsSA}
                />
              </div>
              <div className="flex-1 overflow-x-auto w-full min-w-0">
                <h3 className="font-semibold mb-2">SM</h3>
                <ScheduleTable
                  program="SM"
                  modules={modules}
                  value={scheduleSM}
                  onChange={setScheduleSM}
                  errors={scheduleErrorsSM}
                />
              </div>
            </div>
          ) : batchType === "OPTOM" ? (
            <div>
              <h3 className="font-semibold mb-2">OPTOM</h3>
              <ScheduleTable
                program="OPTOM"
                modules={modules}
                value={scheduleOPTOM}
                onChange={setScheduleOPTOM}
                errors={scheduleErrorsOPTOM}
              />
            </div>
          ) : null}
        </div>
      )}
      {formError && <div className="text-destructive text-sm mt-2">{formError}</div>}
      <div className="flex gap-2 mt-8">
        <Button type="submit" disabled={!isFormValid || submitting}>{submitting ? "Saving..." : submitText}</Button>
        <Link href={cancelHref}>
          <Button type="button" variant="outline">Cancel</Button>
        </Link>
      </div>
    </form>
  );
} 