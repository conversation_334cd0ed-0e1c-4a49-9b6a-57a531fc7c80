import type { Metadata } from 'next';

import { Toaster } from '@/components/ui/sonner';
import { AuthProvider } from '@/lib/provider/AuthProvider';
import QueryProvider from '@/lib/provider/QueryClientProvider';

import './globals.css';

export const metadata: Metadata = {
  title: 'LK Academy',
  description: 'Lenskart Academy Management System',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html className="min-h-full bg-gray-50 text-gray-900" lang="en">
      <body className="min-h-full bg-gradient-to-br from-white to-gray-100 font-sans antialiased">
        <QueryProvider>
          <AuthProvider>{children}</AuthProvider>
        </QueryProvider>
        <Toaster position="top-right" richColors />
      </body>
    </html>
  );
}
