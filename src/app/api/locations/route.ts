import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, Prisma } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const LocationCreateSchema = z.object({
  name: z.string().min(1).max(32),
  circle: z.string().min(1).max(32),
  address: z.string().min(1).max(128),
  type: z.enum(['ACADEMY', 'SATELLITE']),
});

const LocationListQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
});

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const query = Object.fromEntries(url.searchParams.entries());
    const parsed = LocationListQuerySchema.safeParse(query);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid query parameters', details: parsed.error.flatten() }, { status: 400 });
    }
    const { page, limit, name } = parsed.data;
    const where: Prisma.LocationWhereInput = {};
    if (name) {
      where.name = { contains: name };
    }
    const total = await prisma.location.count({ where });
    const locations = await prisma.location.findMany({
      where,
      orderBy: { name: 'asc' },
      skip: (page - 1) * limit,
      take: limit,
    });
    const totalPages = Math.ceil(total / limit);
    return NextResponse.json({
      success: true,
      data: {
        locations,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (err) {
    console.error('Location GET error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    // Auth check
    const auth = await authenticateAndAuthorize(req, 'manage_locations');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const body = await req.json();
    const parsed = LocationCreateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    // Unique name check
    const exists = await prisma.location.findUnique({ where: { name: parsed.data.name } });
    if (exists) {
      return NextResponse.json({ success: false, error: 'Location name already exists' }, { status: 409 });
    }
    const location = await prisma.location.create({
    data: {
      name: parsed.data.name,
      circle: parsed.data.circle,
      address: parsed.data.address,
      type: parsed.data.type,
    }
  });
    return NextResponse.json({ success: true, data: location }, { status: 201 });
  } catch (err) {
    console.error('Location POST error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 