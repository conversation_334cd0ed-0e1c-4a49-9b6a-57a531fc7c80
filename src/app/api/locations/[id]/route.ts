import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, LocationType } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const LocationUpdateSchema = z.object({
  name: z.string().min(1).max(32),
  circle: z.string().min(1).max(32),
  address: z.string().min(1).max(255),
  type: z.nativeEnum(LocationType),
});

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_locations');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid location id' }, { status: 400 });
    }
    const body = await req.json();
    const parsed = LocationUpdateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    // Check if location exists
    const location = await prisma.location.findUnique({ where: { id } });
    if (!location) {
      return NextResponse.json({ success: false, error: 'Location not found' }, { status: 404 });
    }
    // Check for duplicate name
    const duplicate = await prisma.location.findUnique({ where: { name: parsed.data.name } });
    if (duplicate && duplicate.id !== id) {
      return NextResponse.json({ success: false, error: 'Location name already exists' }, { status: 409 });
    }
    const updated = await prisma.location.update({
      where: { id },
      data: {
        name: parsed.data.name,
        circle: parsed.data.circle,
        address: parsed.data.address,
        type: parsed.data.type,
      },
    });
    return NextResponse.json({ success: true, data: updated });
  } catch (err) {
    console.error('Location PUT error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_locations');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid location id' }, { status: 400 });
    }
    // Check if location exists
    const location = await prisma.location.findUnique({ where: { id } });
    if (!location) {
      return NextResponse.json({ success: false, error: 'Location not found' }, { status: 404 });
    }
    await prisma.location.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('Location DELETE error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 