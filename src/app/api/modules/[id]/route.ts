import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const ModuleUpdateSchema = z.object({
  name: z.string().min(1).max(64),
  description: z.string().max(256).optional(),
  duration: z.coerce.number().int().min(1).max(365),
  nature: z.enum(['CRT', 'OJT']),
  program: z.enum(['SA', 'SM', 'OPTOM']),
});

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_modules');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid module id' }, { status: 400 });
    }
    const body = await req.json();
    const parsed = ModuleUpdateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    // Check if module exists
    const existingModule = await prisma.module.findUnique({ where: { id } });
    if (!existingModule) {
      return NextResponse.json({ success: false, error: 'Module not found' }, { status: 404 });
    }
    // Check for duplicate name
    const duplicate = await prisma.module.findFirst({ where: { name: parsed.data.name } });
    if (duplicate && duplicate.id !== id) {
      return NextResponse.json({ success: false, error: 'Module name already exists' }, { status: 409 });
    }
    const updated = await prisma.module.update({ where: { id }, data: parsed.data });
    return NextResponse.json({ success: true, data: updated });
  } catch (err) {
    console.error('Module PUT error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
try {
    const auth = await authenticateAndAuthorize(req, 'manage_modules');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid module id' }, { status: 400 });
    }
    // Check if module exists
    const existingModule = await prisma.module.findUnique({ where: { id } });
    if (!existingModule) {
      return NextResponse.json({ success: false, error: 'Module not found' }, { status: 404 });
    }
    await prisma.module.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('Module DELETE error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 