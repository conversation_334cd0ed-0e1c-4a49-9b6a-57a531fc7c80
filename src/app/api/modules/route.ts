import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, Prisma } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const ModuleCreateSchema = z.object({
  name: z.string().min(1).max(64),
  description: z.string().max(256).optional(),
  duration: z.coerce.number().int().min(1).max(365),
  nature: z.enum(['CRT', 'OJT']),
  program: z.enum(['SA', 'SM', 'OPTOM']),
});

const ModuleListQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
});

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const query = Object.fromEntries(url.searchParams.entries());
    const parsed = ModuleListQuerySchema.safeParse(query);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid query parameters', details: parsed.error.flatten() }, { status: 400 });
    }
    const { page, limit, name } = parsed.data;
    const where: Prisma.ModuleWhereInput = {};
    if (name) {
      where.name = { contains: name };
    }
    const total = await prisma.module.count({ where });
    const modules = await prisma.module.findMany({
      where,
      orderBy: { name: 'asc' },
      skip: (page - 1) * limit,
      take: limit,
    });
    const totalPages = Math.ceil(total / limit);
    return NextResponse.json({
      success: true,
      data: {
        modules,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (err) {
    console.error('Module GET error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    // Auth check
    const auth = await authenticateAndAuthorize(req, 'manage_modules');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const body = await req.json();
    const parsed = ModuleCreateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    // Unique name check
    const exists = await prisma.module.findFirst({ where: { name: parsed.data.name } });
    if (exists) {
      return NextResponse.json({ success: false, error: 'Module name already exists' }, { status: 409 });
    }
    const createdModule = await prisma.module.create({ data: parsed.data });
    return NextResponse.json({ success: true, data: createdModule }, { status: 201 });
  } catch (err) {
    console.error('Module POST error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 