import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const HolidaySchema = z.object({
  date: z.coerce.date(),
  reason: z.string().max(128).optional(),
  locationId: z.coerce.number().int().min(1).optional(),
});

const HolidayListQuerySchema = z.object({
  locationId: z.coerce.number().int().min(1).optional(),
});

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const query = Object.fromEntries(url.searchParams.entries());
    const parsed = HolidayListQuerySchema.safeParse(query);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid query parameters', details: parsed.error.flatten() }, { status: 400 });
    }
    const { locationId } = parsed.data;
    const where: Record<string, unknown> = {};
    if (locationId) where.locationId = locationId;
    const holidays = await prisma.holiday.findMany({
      where,
      orderBy: [{ date: 'asc' }],
      include: { location: true },
    });
    return NextResponse.json({ success: true, data: holidays });
  } catch (err) {
    console.error('Holiday GET error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_holidays');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const body = await req.json();
    const parsed = HolidaySchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    const { date, reason, locationId } = parsed.data;
    // Block Sundays
    if (new Date(date).getDay() === 0) {
      return NextResponse.json({ success: false, error: 'Cannot add a holiday on Sunday' }, { status: 400 });
    }
    // Enforce unique (date, locationId)
    const existing = await prisma.holiday.findFirst({ where: { date, locationId: locationId ?? null } });
    if (existing) {
      return NextResponse.json({ success: false, error: 'Holiday already exists for this date/location' }, { status: 409 });
    }
    const created = await prisma.holiday.create({
      data: { date, reason, locationId: locationId ?? null },
      include: { location: true },
    });
    return NextResponse.json({ success: true, data: created }, { status: 201 });
  } catch (err) {
    console.error('Holiday POST error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 