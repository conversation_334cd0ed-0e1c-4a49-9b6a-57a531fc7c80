import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const HolidaySchema = z.object({
  date: z.coerce.date(),
  reason: z.string().max(128).optional(),
  locationId: z.coerce.number().int().min(1).optional(),
});

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_holidays');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid holiday id' }, { status: 400 });
    }
    const body = await req.json();
    const parsed = HolidaySchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    const { date, reason, locationId } = parsed.data;
    // Block Sundays
    if (new Date(date).getDay() === 0) {
      return NextResponse.json({ success: false, error: 'Cannot set a holiday on Sunday' }, { status: 400 });
    }
    // Check if holiday exists
    const existing = await prisma.holiday.findUnique({ where: { id } });
    if (!existing) {
      return NextResponse.json({ success: false, error: 'Holiday not found' }, { status: 404 });
    }
    // Enforce unique (date, locationId) for update
    const duplicate = await prisma.holiday.findFirst({ where: { date, locationId: locationId ?? null, NOT: { id } } });
    if (duplicate) {
      return NextResponse.json({ success: false, error: 'Holiday already exists for this date/location' }, { status: 409 });
    }
    const updated = await prisma.holiday.update({
      where: { id },
      data: { date, reason, locationId: locationId ?? null },
      include: { location: true },
    });
    return NextResponse.json({ success: true, data: updated });
  } catch (err) {
    console.error('Holiday PUT error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_holidays');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid holiday id' }, { status: 400 });
    }
    const existing = await prisma.holiday.findUnique({ where: { id } });
    if (!existing) {
      return NextResponse.json({ success: false, error: 'Holiday not found' }, { status: 404 });
    }
    await prisma.holiday.delete({ where: { id } });
    return NextResponse.json({ success: true, message: 'Holiday deleted' });
  } catch (err) {
    console.error('Holiday DELETE error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 