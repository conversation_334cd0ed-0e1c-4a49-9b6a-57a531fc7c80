import { DELETE } from './route';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret';

const ADMIN_USER = {
  name: 'Admin User',
  email: '<EMAIL>',
  password: 'AdminPass123!',
  role: 'system_admin',
  permission: 'manage_roles',
};

const OTHER_USER = {
  name: 'Other User',
  email: '<EMAIL>',
  password: 'OtherPass123!',
  role: 'viewer',
  permission: 'view_permissions',
};

let adminUserId: number;
let adminRoleId: number;
let manageRolesPermissionId: number;
let testRoleId: number;
let testPermissionIds: number[] = [];

beforeAll(async () => {
  // Clean up
  await prisma.user.deleteMany({ where: { email: { in: [ADMIN_USER.email, OTHER_USER.email] } } });
  await prisma.role.deleteMany({ where: { name: { in: [ADMIN_USER.role, OTHER_USER.role, 'test_role'] } } });
  await prisma.permission.deleteMany({ where: { name: { in: [ADMIN_USER.permission, OTHER_USER.permission, 'perm1', 'perm2', 'perm3'] } } });
  await prisma.rolePermission.deleteMany({});

  // Seed permissions
  const manageRoles = await prisma.permission.create({ data: { name: ADMIN_USER.permission } });
  manageRolesPermissionId = manageRoles.id;
  const perm1 = await prisma.permission.create({ data: { name: 'perm1' } });
  const perm2 = await prisma.permission.create({ data: { name: 'perm2' } });
  const perm3 = await prisma.permission.create({ data: { name: 'perm3' } });
  testPermissionIds = [perm1.id, perm2.id, perm3.id];

  // Seed roles
  const adminRole = await prisma.role.create({ data: { name: ADMIN_USER.role } });
  adminRoleId = adminRole.id;
  const viewerRole = await prisma.role.create({ data: { name: OTHER_USER.role } });
  const testRole = await prisma.role.create({ data: { name: 'test_role' } });
  testRoleId = testRole.id;

  // Link permission to admin role
  await prisma.rolePermission.create({ data: { roleId: adminRoleId, permissionId: manageRolesPermissionId } });

  // Link test permissions to test role
  for (const pid of testPermissionIds) {
    await prisma.rolePermission.create({ data: { roleId: testRoleId, permissionId: pid } });
  }

  // Seed users
  const adminUser = await prisma.user.create({
    data: {
      name: ADMIN_USER.name,
      email: ADMIN_USER.email,
      passwordHash: 'irrelevant',
      phoneNumber: '9000000003',
      isActive: true,
      userRoles: { create: { roleId: adminRoleId } },
    },
  });
  adminUserId = adminUser.id;
  await prisma.user.create({
    data: {
      name: OTHER_USER.name,
      email: OTHER_USER.email,
      passwordHash: 'irrelevant',
      phoneNumber: '9000000004',
      isActive: true,
      userRoles: { create: { roleId: viewerRole.id } },
    },
  });
});

afterAll(async () => {
  await prisma.$disconnect();
});

function makeToken(permissions: string[]) {
  return jwt.sign({ id: adminUserId, name: ADMIN_USER.name, email: ADMIN_USER.email, roles: [ADMIN_USER.role], permissions }, JWT_SECRET, { expiresIn: '1h' });
}

describe('DELETE /api/deletePermissionsFromRole', () => {
  it('should remove permissions from a role (happy path)', async () => {
    const token = makeToken(['manage_roles']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [testPermissionIds[0], testPermissionIds[1]] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.success).toBe(true);
    expect(body.data.roleId).toBe(testRoleId);
    expect(body.data.removedPermissionIds.sort()).toEqual([testPermissionIds[0], testPermissionIds[1]].sort());
    expect(body.data.totalPermissions).toBe(1);
  });

  it('should not error if removing non-existent associations', async () => {
    const token = makeToken(['manage_roles']);
    // Remove permissions that are already deleted
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [testPermissionIds[0], 999999] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.success).toBe(true);
    // Only valid, existing associations should be reported as removed
    expect(body.data.removedPermissionIds).toEqual([]);
    // Only 1 permission should remain after previous test
    expect(body.data.totalPermissions).toBe(1);
  });

  it('should return 400 for invalid role ID', async () => {
    const token = makeToken(['manage_roles']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: 999999, permissionIds: [testPermissionIds[2]] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toMatch(/Role not found/);
  });

  it('should return 400 for invalid permission ID', async () => {
    const token = makeToken(['manage_roles']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [999999] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toMatch(/Permissions not found/);
  });

  it('should return 401 for missing token', async () => {
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [testPermissionIds[2]] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toMatch(/Authentication required/);
  });

  it('should return 401 for insufficient permissions', async () => {
    const token = makeToken(['view_permissions']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [testPermissionIds[2]] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toMatch(/Insufficient permissions/);
  });

  it('should return 400 for empty permissionIds', async () => {
    const token = makeToken(['manage_roles']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toBeDefined();
  });

  it('should return 400 if role is soft-deleted', async () => {
    // Soft-delete the test role
    await prisma.role.update({ where: { id: testRoleId }, data: { deletedAt: new Date() } });
    const token = makeToken(['manage_roles']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [testPermissionIds[2]] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toMatch(/Role not found/);
    // Restore role for other tests
    await prisma.role.update({ where: { id: testRoleId }, data: { deletedAt: null } });
  });

  it('should return 400 if permission is soft-deleted', async () => {
    // Soft-delete a permission
    await prisma.permission.update({ where: { id: testPermissionIds[2] }, data: { deletedAt: new Date() } });
    const token = makeToken(['manage_roles']);
    const req = new Request('http://localhost/api/deletePermissionsFromRole', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json', cookie: `token=${token}` },
      body: JSON.stringify({ roleId: testRoleId, permissionIds: [testPermissionIds[2]] }),
    });
    const res = await DELETE(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.success).toBe(false);
    expect(body.error).toMatch(/Permissions not found/);
    // Restore permission for other tests
    await prisma.permission.update({ where: { id: testPermissionIds[2] }, data: { deletedAt: null } });
  });
}); 