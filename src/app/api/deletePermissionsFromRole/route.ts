import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

import { NextResponse } from 'next/server';

import { getIpFromRequest } from '@/lib/utils';
import { logAudit } from '@/lib/utils/auditLog';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

// Validation schema
const DeletePermissionsFromRoleSchema = z.object({
  roleId: z.coerce.number().int().min(1),
  permissionIds: z.array(z.coerce.number().int().min(1)).min(1),
});

export async function DELETE(request: Request) {
  // Auth
  const auth = await authenticateAndAuthorize(request, 'manage_roles');
  if (!auth.success) {
    return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
  }

  // Parse body
  let body;
  try {
    body = await request.json();
  } catch {
    return NextResponse.json({ success: false, error: 'Invalid JSON body' }, { status: 400 });
  }
  const parse = DeletePermissionsFromRoleSchema.safeParse(body);
  if (!parse.success) {
    return NextResponse.json({ success: false, error: parse.error.flatten() }, { status: 400 });
  }
  const { roleId, permissionIds } = parse.data;

  // Check role exists and not deleted
  const role = await prisma.role.findUnique({ where: { id: roleId, deletedAt: null } });
  if (!role) {
    return NextResponse.json({ success: false, error: 'Role not found' }, { status: 400 });
  }

  // Check all permissions exist and not deleted
  const permissions = await prisma.permission.findMany({
    where: { id: { in: permissionIds }, deletedAt: null },
    select: { id: true },
  });
  const foundIds = permissions.map((p) => p.id);
  const missing = permissionIds.filter((id) => !foundIds.includes(id));
  if (missing.length > 0) {
    return NextResponse.json(
      { success: false, error: `Permissions not found: ${missing.join(', ')}` },
      { status: 400 },
    );
  }

  // Find existing role-permission associations (not soft-deleted)
  const existing = await prisma.rolePermission.findMany({
    where: {
      roleId,
      permissionId: { in: permissionIds },
    },
    select: { id: true, permissionId: true },
  });
  const toRemove = existing.map((rp) => rp.id);
  const removedPermissionIds = existing.map((rp) => rp.permissionId);

  // Hard-delete the RolePermission associations
  if (toRemove.length > 0) {
    await prisma.rolePermission.deleteMany({
      where: { id: { in: toRemove } },
    });
  }

  // Get total permissions for the role (no soft-delete filter)
  const totalPermissions = await prisma.rolePermission.count({
    where: { roleId },
  });

  console.log({ toRemove });

  const ip = getIpFromRequest(request);

  await logAudit({
    prisma,
    action: 'DELETE_PERMISSION_FROM_ROLE',
    tableName: 'RolePermission',
    recordId: toRemove.toString(),
    userId: auth.userId,
    metadata: JSON.stringify({ ...parse.data, ip }),
  });

  return NextResponse.json({
    success: true,
    data: {
      roleId,
      removedPermissionIds,
      totalPermissions,
    },
  });
}
