// Import the mocked module
import { cookies } from 'next/headers';

import { POST } from './route';

// Mock the cookies function from next/headers
jest.mock('next/headers', () => ({
  cookies: jest.fn(),
}));

describe('POST /api/logout', () => {
  let mockCookiesSet: jest.Mock;

  beforeEach(() => {
    mockCookiesSet = jest.fn();
    // Mock cookies to return an object with set method
    (cookies as jest.Mock).mockResolvedValue({
      set: mockCookiesSet,
    });
    jest.clearAllMocks();
  });

  it('should clear the token cookie and return success', async () => {
    const res = await POST();
    expect(res.status).toBe(200);

    const body = await res.json();
    expect(body).toEqual({ success: true });

    expect(mockCookiesSet).toHaveBeenCalledWith({
      name: 'token',
      value: '',
      path: '/',
      httpOnly: true,
      sameSite: 'strict',
      secure: true,
      maxAge: 0,
    });
  });

  it('should return success even when no token cookie is present', async () => {
    const res = await POST();
    expect(res.status).toBe(200);

    const body = await res.json();
    expect(body).toEqual({ success: true });

    expect(mockCookiesSet).toHaveBeenCalledWith({
      name: 'token',
      value: '',
      path: '/',
      httpOnly: true,
      sameSite: 'strict',
      secure: true,
      maxAge: 0,
    });
  });

  it('should return 500 if cookies() throws an error', async () => {
    (cookies as jest.Mock).mockRejectedValueOnce(new Error('Cookie operation failed'));

    const res = await POST();
    expect(res.status).toBe(500);

    const body = await res.json();
    expect(body).toMatchObject({
      success: false,
      error: 'Logout failed',
      details: expect.anything(),
    });
  });

  it('should return 500 if cookies().set throws an error', async () => {
    mockCookiesSet.mockImplementation(() => {
      throw new Error('Set cookie failed');
    });

    const res = await POST();
    expect(res.status).toBe(500);

    const body = await res.json();
    expect(body).toMatchObject({
      success: false,
      error: 'Logout failed',
      details: expect.anything(),
    });
  });

  it('should always attempt to clear the token cookie regardless of input', async () => {
    const testCases = [
      'token=valid.jwt.token',
      'token=expired.jwt.token',
      'token=invalid.token',
      'otherCookie=value',
      '',
    ];

    for (let i = 0; i < testCases.length; i++) {
      const res = await POST();
      expect(res.status).toBe(200);

      const body = await res.json();
      expect(body).toEqual({ success: true });
    }

    expect(mockCookiesSet).toHaveBeenCalledTimes(testCases.length);
  });
});
