import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST() {
  try {
    // Clear the token cookie by setting it to expire in the past
    (await cookies()).set({
      name: 'token',
      value: '',
      path: '/',
      httpOnly: true,
      sameSite: 'strict',
      secure: true,
      maxAge: 0,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Logout failed', details: error },
      { status: 500 },
    );
  }
}
