import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { CandidateSchema } from '@/lib/schemas/CandidateSchema';
import { logAudit } from '@/lib/utils/auditLog';

const prisma = new PrismaClient();

export async function GET() {
  const candidates = await prisma.candidate.findMany();
  return NextResponse.json(candidates);
}

export async function POST(req: Request) {
  const body = await req.json();
  const parsed = CandidateSchema.safeParse(body);
  if (!parsed.success) {
    return NextResponse.json({ error: parsed.error.errors }, { status: 400 });
  }
  const { id, ...candidateData } = parsed.data; // eslint-disable-line @typescript-eslint/no-unused-vars
  const candidate = await prisma.candidate.create({ data: candidateData });

  // Audit log for candidate creation
  await logAudit({
    prisma,
    action: 'CREATE_CANDIDATE',
    tableName: 'Candidate',
    recordId: candidate.id.toString(),
    // userId: (add user id if available from session/auth),
    newValue: candidate,
    // metadata: (add metadata if available, e.g., IP, user agent)
  });

  return NextResponse.json(candidate, { status: 201 });
} 