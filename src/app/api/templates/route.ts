import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, BatchType } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const DayModuleSchema = z.object({
  moduleId: z.number().int().optional(), // optional for OJT
  startTime: z.string().optional(),
  endTime: z.string().optional(),
});

const DayScheduleSchema = z.object({
  dayNumber: z.number().int().min(1).max(30),
  groupType: z.enum(['SA', 'SM', 'ALL']),
  nature: z.enum(['CRT', 'OJT']),
  modules: z.array(DayModuleSchema),
});

const CreateTemplateSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  batchType: z.enum(['SA_SM', 'OPTOM']),
  schedule: z.array(DayScheduleSchema),
});

function calculateDuration(start: string, end: string): number {
  // start, end: "HH:mm"
  const [sh, sm] = start.split(":").map(Number);
  const [eh, em] = end.split(":").map(Number);
  return (eh * 60 + em - (sh * 60 + sm)) / 60;
}

export async function POST(req: Request) {
  try {
    // Auth check
    const auth = await authenticateAndAuthorize(req, 'manage_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const body = await req.json();
    const parsed = CreateTemplateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    const { name, description, batchType, schedule } = parsed.data;
    // Map OPTOM to OPTUM for backend enum
    const backendBatchType: BatchType = batchType === 'OPTOM' ? 'OPTUM' : batchType as BatchType;
    // Validation: unique name
    const existingByName = await prisma.template.findFirst({ where: { name } });
    if (existingByName) {
      return NextResponse.json({ success: false, error: 'A template with this name already exists.' }, { status: 400 });
    }
    // Validation: 30 days per groupType
    if (batchType === 'SA_SM') {
      const saDays = schedule.filter(d => d.groupType === 'SA').length;
      const smDays = schedule.filter(d => d.groupType === 'SM').length;
      if (saDays !== 30 || smDays !== 30) {
        return NextResponse.json({ success: false, error: 'Schedule must include 30 days for each groupType (SA and SM) for SA_SM.' }, { status: 400 });
      }
    } else if (batchType === 'OPTOM') {
      const allDays = schedule.filter(d => d.groupType === 'ALL').length;
      if (allDays !== 30) {
        return NextResponse.json({ success: false, error: 'Schedule must include 30 days for groupType ALL for OPTOM.' }, { status: 400 });
      }
    }
    // Find max version for this name+batchType
    const maxVersion = await prisma.template.aggregate({
      _max: { version: true },
      where: { name, batchType: backendBatchType },
    });
    const nextVersion = (maxVersion._max.version || 0) + 1;
    // Create template
    const template = await prisma.template.create({
      data: {
        name,
        description,
        batchType: backendBatchType,
        version: nextVersion,
        isActive: true,
        createdBy: auth.userId,
      },
    });
    // Ensure OJT Placeholder module exists (find by name, or create if not found)
    let ojtModule = await prisma.module.findFirst({ where: { name: 'OJT Placeholder' } });
    if (!ojtModule) {
      ojtModule = await prisma.module.create({
        data: {
          name: 'OJT Placeholder',
          duration: 1,
          nature: 'OJT',
          program: 'SA', // or 'ALL' or any valid value
        },
      });
    }
    // Create schedule rows
    const scheduleRows = [];
    for (const day of schedule) {
      if (day.nature === 'OJT') {
        // OJT: one row, use OJT Placeholder moduleId
        scheduleRows.push({
          templateId: template.id,
          dayNumber: day.dayNumber,
          groupType: day.groupType,
          moduleId: ojtModule.id,
          startTime: '', // must be string
          endTime: '',   // must be string
          duration: 0,
          createdBy: auth.userId,
        });
      } else {
        // CRT: one row per module
        for (const mod of day.modules) {
          if (!mod.moduleId || !mod.startTime || !mod.endTime) continue;
          scheduleRows.push({
            templateId: template.id,
            dayNumber: day.dayNumber,
            groupType: day.groupType,
            moduleId: mod.moduleId,
            startTime: mod.startTime,
            endTime: mod.endTime,
            duration: calculateDuration(mod.startTime, mod.endTime),
            createdBy: auth.userId,
          });
        }
      }
    }
    await prisma.scheduleTemplate.createMany({ data: scheduleRows });
    return NextResponse.json({ success: true, data: { templateId: template.id, version: template.version } }, { status: 201 });
  } catch (err) {
    console.error('Template POST error:', err);
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : String(err),
      details: err
    }, { status: 500 });
  }
}

export async function GET(req: Request) {
  try {
    // Auth check
    const auth = await authenticateAndAuthorize(req, 'manage_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    // Parse query params
    const url = new URL(req.url);
    const nameFilter = url.searchParams.get('name');
    const batchTypeFilter = url.searchParams.get('batchType');
    // Get all templates, apply filters
    const allTemplates = await prisma.template.findMany({
      where: {
        isActive: true,
        ...(nameFilter ? { name: { contains: nameFilter } } : {}),
        ...(batchTypeFilter ? { batchType: batchTypeFilter as BatchType } : {}),
      },
      orderBy: [
        { name: 'asc' },
        { version: 'desc' },
      ],
    });
    // Only latest version per name
    const latestByName = Object.values(
      allTemplates.reduce((acc, t) => {
        if (!acc[t.name]) acc[t.name] = t;
        return acc;
      }, {} as Record<string, typeof allTemplates[0]>)
    );
    return NextResponse.json({
      success: true,
      data: latestByName.map(t => ({
        id: t.id,
        name: t.name,
        batchType: t.batchType,
        version: t.version,
        isActive: t.isActive,
        createdAt: t.createdAt,
        updatedAt: t.updatedAt,
      })),
    });
  } catch (err) {
    console.error('Template GET error:', err);
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : String(err),
      details: err
    }, { status: 500 });
  }
} 