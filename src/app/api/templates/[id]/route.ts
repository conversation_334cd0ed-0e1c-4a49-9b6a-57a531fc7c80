import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, BatchType, GroupType, Template } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const DayModuleSchema = z.object({
  moduleId: z.number().int().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
});

const DayScheduleSchema = z.object({
  dayNumber: z.number().int().min(1).max(30),
  groupType: z.enum(['SA', 'SM', 'ALL']),
  nature: z.enum(['CRT', 'OJT']),
  modules: z.array(DayModuleSchema),
});

const UpdateTemplateSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  batchType: z.enum(['SA_SM', 'OPTOM']),
  schedule: z.array(DayScheduleSchema),
});

function calculateDuration(start: string, end: string): number {
  const [sh, sm] = start.split(":").map(Number);
  const [eh, em] = end.split(":").map(Number);
  return (eh * 60 + em - (sh * 60 + sm)) / 60;
}

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Auth check - verify manage_templates permission
    const auth = await authenticateAndAuthorize(req, 'manage_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }

    const templateId = parseInt(params.id);
    if (isNaN(templateId) || typeof templateId !== 'number') {
      return NextResponse.json({ success: false, error: 'Invalid template ID' }, { status: 400 });
    }

    // Get template with schedule details
    const template = await prisma.template.findUnique({
      where: { id: templateId },
      include: {
        scheduleTemplates: {
          include: {
            module: true,
          },
          orderBy: [
            { dayNumber: 'asc' },
            { groupType: 'asc' },
          ],
        },
      },
    });

    if (!template) {
      return NextResponse.json({ success: false, error: 'Template not found' }, { status: 404 });
    }

    // Transform schedule data to match frontend format
    const scheduleMap = new Map();
    
    for (const scheduleItem of template.scheduleTemplates) {
      const key = `${scheduleItem.dayNumber}-${scheduleItem.groupType}`;
      
      if (!scheduleMap.has(key)) {
        scheduleMap.set(key, {
          dayNumber: scheduleItem.dayNumber,
          groupType: scheduleItem.groupType,
          nature: scheduleItem.module.nature,
          modules: [],
        });
      }
      
      const daySchedule = scheduleMap.get(key);
      
      if (scheduleItem.module.nature === 'OJT') {
        // OJT day - no modules needed
        daySchedule.modules = [];
      } else {
        // CRT day - add module details
        daySchedule.modules.push({
          moduleId: scheduleItem.moduleId,
          startTime: scheduleItem.startTime,
          endTime: scheduleItem.endTime,
        });
      }
    }

    const schedule = Array.from(scheduleMap.values());

    // Map backend batchType to frontend format
    const frontendBatchType = template.batchType === 'OPTUM' ? 'OPTOM' : template.batchType;

    return NextResponse.json({
      success: true,
      data: {
        id: template.id,
        name: template.name,
        description: template.description,
        batchType: frontendBatchType,
        version: template.version,
        isActive: template.isActive,
        schedule,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt,
      },
    });
  } catch (err) {
    console.error('Template GET by ID error:', err);
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : String(err),
      details: err
    }, { status: 500 });
  }
}

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Auth check - verify manage_templates permission
    const auth = await authenticateAndAuthorize(req, 'manage_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }

    const templateId = parseInt(params.id);
    if (isNaN(templateId) || typeof templateId !== 'number') {
      return NextResponse.json({ success: false, error: 'Invalid template ID' }, { status: 400 });
    }

    // Check if template exists
    const existingTemplate = await prisma.template.findUnique({
      where: { id: templateId },
    });
    if (!existingTemplate) {
      return NextResponse.json({ success: false, error: 'Template not found' }, { status: 404 });
    }

    const body = await req.json();
    const parsed = UpdateTemplateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }

    const { name, description, batchType, schedule } = parsed.data;
    const backendBatchType: BatchType = batchType === 'OPTOM' ? 'OPTUM' : batchType as BatchType;

    // Validation: 30 days per groupType
    if (batchType === 'SA_SM') {
      const saDays = schedule.filter(d => d.groupType === 'SA').length;
      const smDays = schedule.filter(d => d.groupType === 'SM').length;
      if (saDays !== 30 || smDays !== 30) {
        return NextResponse.json({ success: false, error: 'Schedule must include 30 days for each groupType (SA and SM) for SA_SM.' }, { status: 400 });
      }
    } else if (batchType === 'OPTOM') {
      const allDays = schedule.filter(d => d.groupType === 'ALL').length;
      if (allDays !== 30) {
        return NextResponse.json({ success: false, error: 'Schedule must include 30 days for groupType ALL for OPTOM.' }, { status: 400 });
      }
    }

    // Ensure OJT Placeholder module exists
    let ojtModule = await prisma.module.findFirst({ where: { name: 'OJT Placeholder' } });
    if (!ojtModule) {
      ojtModule = await prisma.module.create({
        data: {
          name: 'OJT Placeholder',
          duration: 1,
          nature: 'OJT',
          program: 'SA',
        },
      });
    }

    // Mark all previous versions as inactive before creating new version
    await prisma.template.updateMany({
      where: {
        name,
        batchType: backendBatchType,
        isActive: true,
        id: { not: templateId },
      },
      data: { isActive: false },
    });

    if (typeof auth.userId !== 'number' || isNaN(auth.userId)) {
      return NextResponse.json({ success: false, error: 'Authenticated user ID is missing or invalid' }, { status: 401 });
    }

    // Prepare schedule rows (do not assign templateId yet)
    const scheduleRows: Array<{
      dayNumber: number;
      groupType: GroupType;
      moduleId: number;
      startTime: string;
      endTime: string;
      duration: number;
      createdBy: number;
    }> = [];
    for (const day of schedule) {
      if (
        typeof day.dayNumber !== 'number' ||
        isNaN(day.dayNumber) ||
        typeof day.groupType !== 'string' ||
        !day.groupType
      ) {
        continue; // skip invalid dayNumber or groupType
      }
      const dayNumber = day.dayNumber;
      const groupType = day.groupType as GroupType;
      if (day.nature === 'OJT') {
        if (!ojtModule || typeof ojtModule.id !== 'number' || isNaN(ojtModule.id)) {
          throw new Error('OJT Placeholder module is missing a valid id');
        }
        scheduleRows.push({
          dayNumber,
          groupType,
          moduleId: ojtModule.id,
          startTime: '',
          endTime: '',
          duration: 0,
          createdBy: auth.userId,
        });
      } else {
        for (const mod of day.modules) {
          if (
            typeof mod.moduleId !== 'number' ||
            isNaN(mod.moduleId) ||
            !mod.startTime ||
            !mod.endTime
          ) {
            continue; // skip invalid modules
          }
          scheduleRows.push({
            dayNumber,
            groupType,
            moduleId: mod.moduleId,
            startTime: mod.startTime,
            endTime: mod.endTime,
            duration: calculateDuration(mod.startTime, mod.endTime),
            createdBy: auth.userId,
          });
        }
      }
    }

    // Check for duplicate schedule rows (unique constraint)
    const seen = new Set();
    for (const row of scheduleRows) {
      const key = `${row.dayNumber}-${row.groupType}-${row.moduleId}`;
      if (seen.has(key)) {
        return NextResponse.json({
          success: false,
          error: `Duplicate schedule entry for day ${row.dayNumber}, group ${row.groupType}, module ${row.moduleId}`,
        }, { status: 400 });
      }
      seen.add(key);
    }

    // All validation passed, perform DB operations in a transaction
    let newTemplate: Template | undefined;
    await prisma.$transaction(async (tx) => {
      // Find max version for this name+batchType
      const maxVersion = await tx.template.aggregate({
        _max: { version: true },
        where: { name, batchType: backendBatchType },
      });
      const nextVersion = (maxVersion._max.version || 0) + 1;
      // Create new template row (new version)
      newTemplate = (await tx.template.create({
        data: {
          name,
          description,
          batchType: backendBatchType,
          version: nextVersion,
          isActive: true,
          createdBy: auth.userId,
        },
      })) as Template;
      // Insert new schedule rows for this version
      await tx.scheduleTemplate.createMany({
        data: scheduleRows.map(row => ({ ...row, templateId: newTemplate!.id })),
      });
    });

    if (!newTemplate) {
      throw new Error('Failed to create new template version');
    }
    return NextResponse.json({
      success: true,
      data: {
        templateId: newTemplate.id,
        version: newTemplate.version,
        message: 'Template updated successfully. New version created.',
      },
    }, { status: 200 });

  } catch (err) {
    console.error('Template PUT error:', err);
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : String(err),
      details: err
    }, { status: 500 });
  }
}
 
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Auth check - verify manage_templates permission
    const auth = await authenticateAndAuthorize(req, 'manage_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }

    const templateId = parseInt(params.id);
    if (isNaN(templateId) || typeof templateId !== 'number') {
      return NextResponse.json({ success: false, error: 'Invalid template ID' }, { status: 400 });
    }

    // Check if template exists
    const existingTemplate = await prisma.template.findUnique({
      where: { id: templateId },
    });
    if (!existingTemplate) {
      return NextResponse.json({ success: false, error: 'Template not found' }, { status: 404 });
    }
    if (!existingTemplate.isActive) {
      return NextResponse.json({ success: false, error: 'Template is already deleted.' }, { status: 400 });
    }

    // Soft delete: set isActive=false, deletedAt, deletedBy for ALL versions with same name and batchType
    await prisma.template.updateMany({
      where: {
        name: existingTemplate.name,
        batchType: existingTemplate.batchType,
        isActive: true,
      },
      data: {
        isActive: false,
        deletedAt: new Date(),
        deletedBy: auth.userId,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'All versions of the template soft deleted successfully.',
      templateId,
    }, { status: 200 });
  } catch (err) {
    console.error('Template DELETE error:', err);
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : String(err),
      details: err
    }, { status: 500 });
  }
}
 