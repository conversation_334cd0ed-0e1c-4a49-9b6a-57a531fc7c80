import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const ScheduleTemplateUpdateSchema = z.object({
  batchType: z.enum(['SA_SM', 'OPTUM']),
  groupType: z.enum(['SA', 'SM', 'ALL']),
  dayNumber: z.coerce.number().int().min(1).max(30),
  moduleId: z.coerce.number().int().min(1),
});

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_schedule_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid schedule template id' }, { status: 400 });
    }
    const body = await req.json();
    const parsed = ScheduleTemplateUpdateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    // Check if entry exists
    const existing = await prisma.scheduleTemplate.findUnique({ where: { id } });
    if (!existing) {
      return NextResponse.json({ success: false, error: 'Schedule template entry not found' }, { status: 404 });
    }
    // Update
    const updated = await prisma.scheduleTemplate.update({
      where: { id },
      data: parsed.data,
      include: { module: true },
    });
    return NextResponse.json({ success: true, data: updated });
  } catch (err) {
    console.error('ScheduleTemplate PUT error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_schedule_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const id = Number(params.id);
    if (isNaN(id) || id < 1) {
      return NextResponse.json({ success: false, error: 'Invalid schedule template id' }, { status: 400 });
    }
    // Check if entry exists
    const existing = await prisma.scheduleTemplate.findUnique({ where: { id } });
    if (!existing) {
      return NextResponse.json({ success: false, error: 'Schedule template entry not found' }, { status: 404 });
    }
    await prisma.scheduleTemplate.delete({ where: { id } });
    return NextResponse.json({ success: true, message: 'Schedule template entry deleted' });
  } catch (err) {
    console.error('ScheduleTemplate DELETE error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 