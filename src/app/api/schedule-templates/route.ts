import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, BatchType } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const ScheduleTemplateEntrySchema = z.object({
  batchType: z.enum(['SA_SM', 'OPTUM']),
  groupType: z.enum(['SA', 'SM', 'ALL']),
  dayNumber: z.coerce.number().int().min(1).max(30),
  moduleId: z.coerce.number().int().min(1),
});

const ScheduleTemplateBulkSchema = z.array(ScheduleTemplateEntrySchema)
  .superRefine((arr, ctx) => {
    if (arr.length === 0) return;
    const batchType = arr[0].batchType;
    if (batchType === 'SA_SM') {
      const groupCounts = arr.reduce((acc, curr) => {
        acc[curr.groupType] = (acc[curr.groupType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      if (groupCounts['SA'] !== 30 || groupCounts['SM'] !== 30) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'For SA_SM, must have 30 SA and 30 SM entries',
        });
      }
    } else if (batchType === 'OPTUM') {
      const groupCounts = arr.reduce((acc, curr) => {
        acc[curr.groupType] = (acc[curr.groupType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      if (groupCounts['ALL'] !== 30 || Object.keys(groupCounts).length !== 1) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'For OPTUM, must have 30 ALL entries',
        });
      }
    } else {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Unsupported batchType',
      });
    }
  });

const ScheduleTemplateListQuerySchema = z.object({
  batchType: z.string().optional(),
  groupType: z.string().optional(),
  dayNumber: z.coerce.number().int().min(1).max(30).optional(),
  moduleId: z.coerce.number().int().min(1).optional(),
});

export async function GET(req: Request) {
  try {
    // Auth check
    const auth = await authenticateAndAuthorize(req, 'manage_schedule_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const url = new URL(req.url);
    const query = Object.fromEntries(url.searchParams.entries());
    const parsed = ScheduleTemplateListQuerySchema.safeParse(query);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid query parameters', details: parsed.error.flatten() }, { status: 400 });
    }
    const { batchType, groupType, dayNumber, moduleId } = parsed.data;
    const where: { [key: string]: unknown } = {};
    if (batchType) where.batchType = batchType as BatchType;
    if (dayNumber) where.dayNumber = dayNumber;
    if (moduleId) where.moduleId = moduleId;
    const templates = await prisma.scheduleTemplate.findMany({
      where,
      orderBy: [
        { batchType: 'asc' },
        { dayNumber: 'asc' },
      ],
      include: { module: true },
    });
    // Workaround: filter by groupType in JS if provided
    const filtered = groupType ? templates.filter(t => t.groupType === groupType) : templates;
    return NextResponse.json({ success: true, data: filtered });
  } catch (err) {
    console.error('ScheduleTemplate GET error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    // Auth check
    const auth = await authenticateAndAuthorize(req, 'manage_schedule_templates');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const body = await req.json();
    const parsed = ScheduleTemplateBulkSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    const entries = parsed.data;
    // Upsert all 30 entries for the given batchType and groupType
    const results = [];
    for (const entry of entries) {
      // Workaround: findFirst without groupType in where, then filter in JS
      const candidates = await prisma.scheduleTemplate.findMany({
        where: {
          batchType: entry.batchType,
          dayNumber: entry.dayNumber,
        },
      });
      const existing = candidates.find(t => t.groupType === entry.groupType);
      let upserted;
      if (existing) {
        upserted = await prisma.scheduleTemplate.update({
          where: { id: existing.id },
          data: { moduleId: entry.moduleId },
          include: { module: true },
        });
      } else {
        upserted = await prisma.scheduleTemplate.create({
          data: entry,
          include: { module: true },
        });
      }
      results.push(upserted);
    }
    return NextResponse.json({ success: true, data: results }, { status: 201 });
  } catch (err) {
    console.error('ScheduleTemplate POST error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 