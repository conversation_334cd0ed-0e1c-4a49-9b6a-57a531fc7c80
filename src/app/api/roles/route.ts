import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';
import type { JwtPayload } from 'jsonwebtoken';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

// Zod schema for query validation
const RolesQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
  sort: z.enum(['id', 'name', 'createdAt', 'updatedAt']).default('name'),
  order: z.enum(['asc', 'desc']).default('asc'),
});

// Helper: Extract and verify JWT from cookies
interface RoleJwtPayload extends JwtPayload {
  permissions: string[];
}


// GET /api/roles
export async function GET(request: Request) {
  try {
    // Authenticate
    const auth = await authenticateAndAuthorize(request, 'view_roles');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }

    // Parse and validate query params
    const url = new URL(request.url);
    const query = Object.fromEntries(url.searchParams.entries());
    const parseResult = RolesQuerySchema.safeParse(query);
    if (!parseResult.success) {
      return NextResponse.json({ success: false, error: 'Invalid query parameters', details: parseResult.error.flatten() }, { status: 400 });
    }
    const { page, limit, name, sort, order } = parseResult.data;

    // Build Prisma query
    const where: any = {};
    if (name) {
      where.name = { contains: name };
    }

    // Count total
    const total = await prisma.role.count({ where });

    // Query roles with permissions
    const roles = await prisma.role.findMany({
      where,
      orderBy: { [sort]: order },
      skip: (page - 1) * limit,
      take: limit,
      include: {
        rolePermissions: {   // getting permissions for each role
          include: {
            permission: { select: { id: true, name: true } },
          },
        },
      },
    });

    // Pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        roles: roles.map(r => ({
          id: r.id,
          name: r.name,
          createdAt: r.createdAt,
          updatedAt: r.updatedAt,
          permissions: r.rolePermissions.map(rp => rp.permission),
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext,
          hasPrev,
        },
      },
    });
  } catch (err: any) {
    console.error('Roles API error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 