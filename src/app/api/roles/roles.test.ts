import { GET } from './route';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'testsecret';

// Helper to create JWT
function makeToken(permissions = ['view_roles']) {
  return jwt.sign(
    {
      id: 1,
      name: 'Test User',
      email: '<EMAIL>',
      roles: ['Admin'],
      permissions,
    },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
}

describe('GET /api/roles', () => {
  beforeEach(async () => {
    // Seed roles and permissions
    await prisma.rolePermission?.deleteMany?.();
    await prisma.permission.deleteMany();
    await prisma.role.deleteMany();
    const perm1 = await prisma.permission.create({ data: { name: 'view_roles' } });
    const perm2 = await prisma.permission.create({ data: { name: 'edit_roles' } });
    const adminRole = await prisma.role.create({ data: { name: 'Admin' } });
    const userRole = await prisma.role.create({ data: { name: 'User' } });
    // Link permissions to roles via join table
    await prisma.rolePermission.create({ data: { roleId: adminRole.id, permissionId: perm1.id } });
    await prisma.rolePermission.create({ data: { roleId: adminRole.id, permissionId: perm2.id } });
    await prisma.rolePermission.create({ data: { roleId: userRole.id, permissionId: perm1.id } });
  });

  afterEach(async () => {
    await prisma.rolePermission?.deleteMany?.();
    await prisma.role.deleteMany();
    await prisma.permission.deleteMany();
  });

  it('returns 401 if no token', async () => {
    const req = new Request('http://localhost/api/roles', { method: 'GET' });
    const res = await GET(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.success).toBe(false);
  });

  it('returns 401 if token is invalid', async () => {
    const req = new Request('http://localhost/api/roles', {
      method: 'GET',
      headers: { cookie: 'token=invalid' },
    });
    const res = await GET(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.success).toBe(false);
  });

  it('returns 401 if user lacks permission', async () => {
    const token = makeToken(['view_permissions']);
    const req = new Request('http://localhost/api/roles', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.success).toBe(false);
  });

  it('returns paginated roles with permissions', async () => {
    const token = makeToken(['view_roles']);
    const req = new Request('http://localhost/api/roles', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.success).toBe(true);
    expect(Array.isArray(body.data.roles)).toBe(true);
    expect(body.data.pagination).toBeDefined();
    expect(body.data.roles[0]).toHaveProperty('permissions');
  });

  it('filters by name', async () => {
    const token = makeToken(['view_roles']);
    const req = new Request('http://localhost/api/roles?name=Admin', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.data.roles.length).toBe(1);
    expect(body.data.roles[0].name).toBe('Admin');
  });

  it('sorts by name desc', async () => {
    const token = makeToken(['view_roles']);
    const req = new Request('http://localhost/api/roles?sort=name&order=desc', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.data.roles[0].name).toBe('User');
  });

  it('paginates results', async () => {
    const token = makeToken(['view_roles']);
    const req = new Request('http://localhost/api/roles?page=2&limit=1', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.data.roles.length).toBe(1);
    expect(body.data.pagination.page).toBe(2);
  });

  it('returns empty array if no match', async () => {
    const token = makeToken(['view_roles']);
    const req = new Request('http://localhost/api/roles?name=NoSuchRole', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.data.roles.length).toBe(0);
  });

  it('returns 400 for invalid params', async () => {
    const token = makeToken(['view_roles']);
    const req = new Request('http://localhost/api/roles?page=0', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.success).toBe(false);
  });
}); 