import { NextRequest } from 'next/server';
import { BatchType } from '@prisma/client';
import { getBatchSchedulePreview } from '@/service/batchSchedule.service';

/**
 * GET /api/batchSchedule/preview
 * Generates a batch schedule preview and trainer assignments for a given batch type, location, and start date.
 * - Returns a flat schedule (one per (day, date, groupType, module), holidays included)
 * - Returns assignments for each batch, assigning trainers based on availability and qualification
 *
 * Query params:
 *   - batchType: string (optional)
 *   - startDay: string (optional, ISO date)
 *   - location: string (optional)
 *   - batchCount: number (optional, default 1)
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const batchTypeParam = searchParams.get('batchType');
    const batchType = batchTypeParam ? (batchTypeParam as BatchType) : undefined;
    const startDay = searchParams.get('startDay') || undefined;
    const location = searchParams.get('location') || undefined;
    const batchCountParam = searchParams.get('batchCount');
    const batchCount = batchCountParam && !isNaN(Number(batchCountParam)) && Number(batchCountParam) > 0 ? Number(batchCountParam) : 1;

    const result = await getBatchSchedulePreview({
      batchType,
      startDay,
      location,
      batchCount
    });

    return Response.json(result);
  } catch (error) {
    console.error('Error generating batch schedule preview:', error);
    return Response.json(
      { error: 'Failed to generate batch schedule preview.' },
      { status: 500 }
    );
  }
} 