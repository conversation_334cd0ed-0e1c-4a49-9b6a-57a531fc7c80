import { NextRequest } from 'next/server';
import { createRoasterWithBatches } from '@/service/batchSchedule.service';

/**
 * POST /api/batchSchedule/confirm
 * Creates a new roaster, batches, and batch modules as per the confirmed schedule.
 * Expects body: { roasterName, createdBy, batches: [ ... ] }
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { roasterName, createdBy, batches } = body;
    if (!roasterName || !batches || !Array.isArray(batches)) {
      return Response.json({ error: 'Invalid request format.' }, { status: 400 });
    }
    const result = await createRoasterWithBatches({ roasterName, createdBy, batches });
    return Response.json(result);
  } catch (error) {
    console.error('Error creating roaster:', error);
    return Response.json({ error: 'Failed to create roaster.' }, { status: 500 });
  }
} 