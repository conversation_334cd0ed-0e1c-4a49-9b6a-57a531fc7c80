import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';

import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret'; // Replace in production
const JWT_EXPIRES_IN = '1d';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const parsed = LoginSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ sucess: false, error: parsed.error.message }, { status: 400 });
    }
    const { email, password } = parsed.data;
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: { permission: true },
                },
              },
            },
          },
        },
      },
    });
    if (!user || !user.passwordHash) {
      return NextResponse.json({ sucess: false, error: 'Invalid credentials' }, { status: 401 });
    }
    if (!user.isActive) {
      return NextResponse.json({ error: 'User is not active' }, { status: 401 });
    }
    const valid = await bcrypt.compare(password, user.passwordHash);
    if (!valid) {
      return NextResponse.json({ sucess: false, error: 'Invalid credentials' }, { status: 401 });
    }
    // Collect roles and permissions
    const roles = user.userRoles.map((ur: { role: { name: string } }) => ur.role.name);
    const permissions = [
      ...new Set(
        user.userRoles.flatMap(
          (ur: { role: { rolePermissions: { permission: { name: string } }[] } }) =>
            ur.role.rolePermissions.map(
              (rp: { permission: { name: string } }) => rp.permission.name,
            ),
        ),
      ),
    ];
    const payload = {
      id: user.id,
      name: user.name,
      email: user.email,
      roles,
      permissions,
    };
    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    const response = NextResponse.json({ success: true, error: null });
    response.headers.set(
      'Set-Cookie',
      `token=${token}; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict; Secure`,
    );
    return response;
  } catch (err) {
    console.error('Login error:', err);
    return NextResponse.json({ sucess: false, error: 'Internal server error' }, { status: 500 });
  }
}
