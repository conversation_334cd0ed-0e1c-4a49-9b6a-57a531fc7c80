import { POST } from './route';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

const TEST_USER = {
  name: 'Test User',
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'candidate',
  permission: 'login',
};

let userId: number;
let roleId: number;
let permissionId: number;

beforeAll(async () => {
  // Clean up any previous test data
  await prisma.user.deleteMany({ where: { email: TEST_USER.email } });
  await prisma.role.deleteMany({ where: { name: TEST_USER.role } });
  await prisma.permission.deleteMany({ where: { name: TEST_USER.permission } });

  // Seed permission
  const permission = await prisma.permission.create({ data: { name: TEST_USER.permission } });
  permissionId = permission.id;

  // Seed role
  const role = await prisma.role.create({ data: { name: TEST_USER.role } });
  roleId = role.id;

  // Link permission to role
  await prisma.rolePermission.create({ data: { roleId, permissionId } });

  // Seed test user with hashed password
  const passwordHash = await bcrypt.hash(TEST_USER.password, 10);
  const user = await prisma.user.create({
    data: {
      name: TEST_USER.name,
      email: TEST_USER.email,
      passwordHash,
      phoneNumber: '9999999999',
      isActive: true,
    },
  });
  userId = user.id;

  // Link user to role
  await prisma.userRole.create({ data: { userId, roleId } });
});

afterAll(async () => {
  // Remove test data
  await prisma.userRole.deleteMany({ where: { userId, roleId } });
  await prisma.user.deleteMany({ where: { id: userId } });
  await prisma.rolePermission.deleteMany({ where: { roleId, permissionId } });
  await prisma.role.deleteMany({ where: { id: roleId } });
  await prisma.permission.deleteMany({ where: { id: permissionId } });
});

describe('POST /api/login', () => {
  it('should return 400 for invalid input', async () => {
    const req = new Request('http://localhost/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: 'not-an-email', password: '123' }),
    });
    const res = await POST(req);
    expect(res.status).toBe(400);
    const body = await res.json();
    expect(body.error).toBeDefined();
  });

  it('should return 401 for invalid credentials', async () => {
    const req = new Request('http://localhost/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: TEST_USER.email, password: 'WrongPassword' }),
    });
    const res = await POST(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.error).toBeDefined();
  });

  it('should return success and set JWT cookie for valid login', async () => {
    const req = new Request('http://localhost/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: TEST_USER.email, password: TEST_USER.password }),
    });
    const res = await POST(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.success).toBe(true);
    const setCookie = res.headers.get('set-cookie');
    expect(setCookie).not.toBeNull();
    expect(setCookie).toMatch(/HttpOnly/);
    expect(setCookie).toMatch(/token=/);
    // Decode JWT and check payload
    const token = setCookie!.split('token=')[1].split(';')[0];
    const payload = jwt.decode(token) as Record<string, unknown>;
    expect(payload).toHaveProperty('email', TEST_USER.email);
    expect(payload).toHaveProperty('name', TEST_USER.name);
    expect(payload).toHaveProperty('roles');
    expect(payload).toHaveProperty('permissions');
  });
}); 