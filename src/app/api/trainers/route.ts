import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient, Prisma, BatchType } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

// --- Zod Schemas ---
const TrainerListQuerySchema = z.object({
  name: z.string().optional(),
  email: z.string().optional(),
  isAvailable: z.coerce.boolean().optional(),
  availableDate: z.coerce.date().optional(),
  skill: z.string().optional(),
  batchName: z.string().optional(),
  batchDate: z.coerce.date().optional(),
  batchType: z.string().optional(),
  location: z.string().optional(),
  locationId: z.coerce.number().int().optional(),
  module: z.string().optional(),
});

const UserSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  phoneNumber: z.string().min(5),
  passwordHash: z.string().min(8),
  gender: z.string().optional(),
  age: z.coerce.number().int().optional(),
  designation: z.string().optional(),
  homeCity: z.string().optional(),
});

const TrainerCreateSchema = z.object({
  user: UserSchema,
  isLeadFacilitator: z.boolean(),
  skills: z.array(z.number().int()).optional(),
  trainerModules: z.array(z.number().int()).optional(),
  availability: z.array(z.object({
    from: z.coerce.date(),
    to: z.coerce.date(),
  })).optional(),
  locationId: z.number().int(),
});

// --- GET: List Trainers ---
export async function GET(req: Request) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_trainers');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const url = new URL(req.url);
    const query = Object.fromEntries(url.searchParams.entries());
    const parsed = TrainerListQuerySchema.safeParse(query);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid query parameters', details: parsed.error.flatten() }, { status: 400 });
    }
    const {
      name, email, isAvailable, availableDate, skill,
      batchName, batchDate, batchType, location, locationId, module
    } = parsed.data;

    // Build Prisma where clause
    const where: Prisma.TrainerWhereInput = {
      user: {
        isActive: true,
        ...(name ? { name: { contains: name } } : {}),
        ...(email ? { email: { contains: email } } : {}),
      },
      ...(locationId ? { locationId } : {}),
      ...(skill ? {
        skills: {
          some: {
            skill: {
              name: { contains: skill }
            }
          }
        }
      } : {}),
      ...(isAvailable !== undefined || availableDate ? {
        availability: {
          some: {
            ...(isAvailable !== undefined ? { isAvailable } : {}),
            ...(availableDate ? { availableDate } : {}),
          }
        }
      } : {}),
      ...(batchName || batchDate || batchType || location || module ? {
        batchModules: {
          some: {
            ...(batchName ? { batch: { batchName: { contains: batchName } } } : {}),
            ...(batchDate ? { batch: { batchDate } } : {}),
            ...(batchType ? { batch: { batchType: batchType as BatchType } } : {}),
            ...(location ? { batch: { location: { name: { contains: location } } } } : {}),
            ...(module ? { module: { name: { contains: module } } } : {}),
          }
        }
      } : {}),
    };

    const trainers = await prisma.trainer.findMany({
      where,
      include: {
        user: true,
        location: true,
        skills: { include: { skill: true } },
        availability: true,
        trainerModules: { include: { module: true } },
        batchModules: {
          include: {
            batch: { include: { location: true } },
            module: true,
          }
        },
      },
      orderBy: [{ id: 'asc' }],
    });

    // Format response
    const data = trainers.map(tr => ({
      id: tr.id,
      isLeadFacilitator: tr.isLeadFacilitator,
      user: {
        id: tr.user.id,
        name: tr.user.name,
        email: tr.user.email,
        phoneNumber: tr.user.phoneNumber,
        gender: tr.user.gender,
        age: tr.user.age,
        designation: tr.user.designation,
        homeCity: tr.user.homeCity,
      },
      location: tr.location ? { id: tr.location.id, name: tr.location.name } : null,
      skills: tr.skills.map(ts => ({ id: ts.skill.id, name: ts.skill.name })),
      availability: tr.availability.map(a => ({ availableDate: a.availableDate, isAvailable: a.isAvailable })),
      trainerModules: tr.trainerModules.map(tm => ({ id: tm.module.id, name: tm.module.name })),
      batchModules: tr.batchModules.map(bm => ({
        batchName: bm.batch.batchName,
        batchDate: bm.batch.batchDate,
        batchType: bm.batch.batchType,
        location: bm.batch.location?.name,
        module: bm.module.name,
      })),
    }));

    return NextResponse.json({ success: true, data });
  } catch (err) {
    console.error('Trainer GET error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

// --- POST: Create Trainer ---
export async function POST(req: Request) {
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_trainers');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const body = await req.json();
    const parsed = TrainerCreateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    const { user, isLeadFacilitator, skills, trainerModules, availability, locationId } = parsed.data;

    let allDates: { availableDate: Date; isAvailable: boolean }[] = [];
    if (availability && availability.length > 0) {
      // Reject if any range starts in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const pastRanges = availability.filter(range => new Date(range.from) < today);
      if (pastRanges.length > 0) {
        return NextResponse.json({ success: false, error: 'Availability range cannot start in the past', ranges: pastRanges }, { status: 400 });
      }
      // Expand all ranges into dates, filter out Sundays and past dates
      let minDate: Date | null = null;
      let maxDate: Date | null = null;
      const allAvailableDates: Set<string> = new Set();
      const allInputDates: string[] = [];
      const pastDates: string[] = [];
      const sundayDates: string[] = [];
      for (const range of availability) {
        // eslint-disable-next-line prefer-const
        let d = new Date(range.from);
        const end = new Date(range.to);
        while (d <= end) {
          const dateStr = d.toISOString().slice(0, 10);
          allInputDates.push(dateStr);
          if (d < today) {
            pastDates.push(dateStr);
          } else if (d.getDay() === 0) {
            sundayDates.push(dateStr);
          } else {
            allAvailableDates.add(dateStr);
            if (!minDate || d < minDate) minDate = new Date(d);
            if (!maxDate || d > maxDate) maxDate = new Date(d);
          }
          d.setDate(d.getDate() + 1);
        }
      }
      if (allAvailableDates.size === 0) {
        if (pastDates.length === allInputDates.length) {
          return NextResponse.json({ success: false, error: 'All available dates are in the past', dates: pastDates }, { status: 400 });
        }
        if (sundayDates.length === allInputDates.length) {
          return NextResponse.json({ success: false, error: 'All available dates are Sundays', dates: sundayDates }, { status: 400 });
        }
        // We'll check for holidays below, but if all are filtered for mixed reasons:
        return NextResponse.json({ success: false, error: 'No valid available dates', pastDates, sundayDates }, { status: 400 });
      }
      if (!minDate || !maxDate) {
        return NextResponse.json({ success: false, error: 'No valid available dates (internal: min/max date missing)' }, { status: 400 });
      }
      // Check for holidays
      const holidays = await prisma.holiday.findMany({
        where: {
          date: {
            gte: minDate,
            lte: maxDate,
          },
        },
      });
      const holidayDates = holidays.map(h => h.date.toISOString().slice(0, 10));
      const conflictDates = Array.from(allAvailableDates as Set<string>).filter((date: string) => holidayDates.includes(date));
      if (conflictDates.length > 0) {
        if (conflictDates.length === allAvailableDates.size) {
          return NextResponse.json({ success: false, error: 'All available dates are holidays', holidayDates: conflictDates }, { status: 400 });
        }
        return NextResponse.json({ success: false, error: 'Some available dates are holidays', holidayDates: conflictDates }, { status: 400 });
      }
      // Build all dates in the range
      allDates = [];
      for (let d = new Date(minDate); d <= maxDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().slice(0, 10);
        allDates.push({
          availableDate: new Date(dateStr),
          isAvailable: allAvailableDates.has(dateStr),
        });
      }
    }

    // All validation is done above. Now perform all DB writes in a transaction.
    const result = await prisma.$transaction(async (tx) => {
      // Check for existing user (repeat check inside transaction for safety)
      const existingUser = await tx.user.findFirst({
        where: {
          OR: [
            { email: user.email },
            { phoneNumber: user.phoneNumber },
          ]
        }
      });
      if (existingUser) {
        throw new Error('User with this email or phone number already exists');
      }
      // Create user
      const createdUser = await tx.user.create({
        data: {
          ...user,
          isActive: true,
        }
      });
      // Create trainer
      const createdTrainer = await tx.trainer.create({
        data: {
          userId: createdUser.id,
          isLeadFacilitator,
          locationId,
        }
      });
      // Add skills
      if (skills && skills.length > 0) {
        await tx.trainerSkill.createMany({
          data: skills.map(skillId => ({ trainerId: createdTrainer.id, skillId })),
          skipDuplicates: true,
        });
      }
      // Add trainerModules
      if (trainerModules && trainerModules.length > 0) {
        await tx.trainerModule.createMany({
          data: trainerModules.map(moduleId => ({ trainerId: createdTrainer.id, moduleId })),
          skipDuplicates: true,
        });
      }
      // Add availability
      if (allDates.length > 0) {
        await tx.trainerAvailability.createMany({
          data: allDates.map(a => ({
            trainerId: createdTrainer.id,
            availableDate: a.availableDate,
            isAvailable: a.isAvailable,
          })),
          skipDuplicates: true,
        });
      } else {
        // If not provided, mark as available all days except Sundays for next 90 days
        const today = new Date();
        const availData = [];
        for (let i = 0; i < 90; i++) {
          const d = new Date(today);
          d.setDate(today.getDate() + i);
          if (d.getDay() !== 0) {
            availData.push({ trainerId: createdTrainer.id, availableDate: d, isAvailable: true });
          }
        }
        if (availData.length > 0) {
          await tx.trainerAvailability.createMany({ data: availData, skipDuplicates: true });
        }
      }
      return createdTrainer.id;
    });
    return NextResponse.json({ success: true, data: { trainerId: result } }, { status: 201 });
  } catch (err) {
    console.error('Trainer POST error:', err);
    if (err instanceof Error && err.message === 'User with this email or phone number already exists') {
      return NextResponse.json({ success: false, error: err.message }, { status: 409 });
    }
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 