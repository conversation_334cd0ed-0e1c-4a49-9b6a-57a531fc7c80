import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

const TrainerUpdateSchema = z.object({
  locationId: z.number().int().optional(),
  user: z.object({
    name: z.string().min(1).optional(),
    email: z.string().email().optional(),
    phoneNumber: z.string().min(5).optional(),
  }).optional(),
  skills: z.array(z.number().int()).optional(),
  trainerModules: z.array(z.number().int()).optional(),
  availability: z.array(z.object({
    from: z.coerce.date(),
    to: z.coerce.date(),
  })).optional(),
});

export async function PUT(req: Request, context: { params: { id: string } }) {
  const { params } = await context;
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_trainers');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const trainerId = parseInt(params.id, 10);
    if (isNaN(trainerId)) {
      return NextResponse.json({ success: false, error: 'Invalid trainer ID' }, { status: 400 });
    }
    // Check if trainer exists
    const trainer = await prisma.trainer.findUnique({ where: { id: trainerId } });
    if (!trainer) {
      return NextResponse.json({ success: false, error: 'Trainer not found' }, { status: 404 });
    }
    const body = await req.json();
    const parsed = TrainerUpdateSchema.safeParse(body);
    if (!parsed.success) {
      return NextResponse.json({ success: false, error: 'Invalid input', details: parsed.error.flatten() }, { status: 400 });
    }
    const { locationId, user, skills, trainerModules, availability } = parsed.data;

    // Update Availability (explicit per-day records)
    let allDates: { availableDate: Date; isAvailable: boolean }[] = [];
    if (availability) {
      // Expand all ranges into dates, filter out Sundays and past dates
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const allAvailableDates = new Set();
      let minDate = null;
      let maxDate = null;
      for (const range of availability) {
        const d = new Date(range.from);
        const end = new Date(range.to);
        while (d <= end) {
          const dateStr = d.toISOString().slice(0, 10);
          if (d.getDay() !== 0 && d >= today) {
            allAvailableDates.add(dateStr);
            if (!minDate || d < minDate) minDate = new Date(d);
            if (!maxDate || d > maxDate) maxDate = new Date(d);
          }
          d.setDate(d.getDate() + 1);
        }
      }
      if (!minDate || !maxDate) {
        return NextResponse.json({ success: false, error: 'No valid available dates' }, { status: 400 });
      }
      // Check for holidays
      const holidays = await prisma.holiday.findMany({
        where: {
          date: {
            gte: minDate,
            lte: maxDate,
          },
        },
      });
      const holidayDates = holidays.map(h => h.date.toISOString().slice(0, 10));
      const conflictDates = Array.from(allAvailableDates as Set<string>).filter((date: string) => holidayDates.includes(date));
      if (conflictDates.length > 0) {
        return NextResponse.json({ success: false, error: 'Some available dates are holidays', holidayDates: conflictDates }, { status: 400 });
      }
      // Build all dates in the range
      for (let d = new Date(minDate); d <= maxDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().slice(0, 10);
        allDates.push({
          availableDate: new Date(dateStr),
          isAvailable: allAvailableDates.has(dateStr),
        });
      }
    }

    // All validation is done above. Now perform all DB writes in a transaction.
    await prisma.$transaction(async (tx) => {
      // Update Trainer
      const trainerUpdate: Partial<{ locationId: number }> = {};
      if (locationId !== undefined) {
        const location = await tx.location.findUnique({ where: { id: locationId } });
        if (!location) {
          throw new Error('Location not found');
        }
        trainerUpdate.locationId = locationId;
      }
      await tx.trainer.update({
        where: { id: trainerId },
        data: trainerUpdate,
      });

      // Update User
      if (user) {
        await tx.trainer.update({
          where: { id: trainerId },
          data: {
            user: {
              update: user,
            },
          },
        });
      }

      // Update Skills
      if (skills) {
        await tx.trainerSkill.deleteMany({ where: { trainerId } });
        if (skills.length > 0) {
          await tx.trainerSkill.createMany({
            data: skills.map(skillId => ({ trainerId, skillId })),
            skipDuplicates: true,
          });
        }
      }

      // Update TrainerModules
      if (trainerModules) {
        await tx.trainerModule.deleteMany({ where: { trainerId } });
        if (trainerModules.length > 0) {
          await tx.trainerModule.createMany({
            data: trainerModules.map(moduleId => ({ trainerId, moduleId })),
            skipDuplicates: true,
          });
        }
      }

      // Update Availability (explicit per-day records)
      if (availability) {
        // Delete all existing availability records for this trainer in the range
        if (allDates.length > 0) {
          const minDate = allDates[0].availableDate;
          const maxDate = allDates[allDates.length - 1].availableDate;
          await tx.trainerAvailability.deleteMany({
            where: {
              trainerId,
              availableDate: {
                gte: minDate,
                lte: maxDate,
              },
            },
          });
          // Insert new records
          await tx.trainerAvailability.createMany({
            data: allDates.map(a => ({
              trainerId,
              availableDate: a.availableDate,
              isAvailable: a.isAvailable,
            })),
            skipDuplicates: true,
          });
        }
      }
    });
    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('Trainer PUT error:', err);
    if (err instanceof Error && err.message === 'Location not found') {
      return NextResponse.json({ success: false, error: err.message }, { status: 400 });
    }
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request, context: { params: { id: string } }) {
  const { params } = await context;
  try {
    const auth = await authenticateAndAuthorize(req, 'manage_trainers');
    if (!auth.success) {
      return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
    }
    const trainerId = parseInt(params.id, 10);
    if (isNaN(trainerId)) {
      return NextResponse.json({ success: false, error: 'Invalid trainer ID' }, { status: 400 });
    }
    // Check if trainer exists
    const trainer = await prisma.trainer.findUnique({ where: { id: trainerId } });
    if (!trainer) {
      return NextResponse.json({ success: false, error: 'Trainer not found' }, { status: 404 });
    }
    // Delete related records (skills, modules, availability)
    await prisma.trainerSkill.deleteMany({ where: { trainerId } });
    await prisma.trainerModule.deleteMany({ where: { trainerId } });
    await prisma.trainerAvailability.deleteMany({ where: { trainerId } });
    // Delete the trainer
    await prisma.trainer.delete({ where: { id: trainerId } });
    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('Trainer DELETE error:', err);
    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });
  }
} 