import { GET } from './route';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret';

const testPayload = {
  id: 123,
  name: 'Test User',
  email: '<EMAIL>',
  roles: ['candidate'],
  permissions: ['login'],
};

describe('/api/me', () => {
  it('should return 401 if no token cookie is present', async () => {
    const req = new Request('http://localhost/api/me', { method: 'GET' });
    const res = await GET(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.error).toBeDefined();
  });

  it('should return 401 if token is invalid', async () => {
    const req = new Request('http://localhost/api/me', {
      method: 'GET',
      headers: { cookie: 'token=invalidtoken' },
    });
    const res = await GET(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.error).toBeDefined();
  });

  it('should return 401 if token is expired', async () => {
    const expiredToken = jwt.sign(testPayload, JWT_SECRET, { expiresIn: -10 });
    const req = new Request('http://localhost/api/me', {
      method: 'GET',
      headers: { cookie: `token=${expiredToken}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.error).toBeDefined();
  });

  it('should return payload if token is valid', async () => {
    const token = jwt.sign(testPayload, JWT_SECRET, { expiresIn: '1h' });
    const req = new Request('http://localhost/api/me', {
      method: 'GET',
      headers: { cookie: `token=${token}` },
    });
    const res = await GET(req);
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body).toMatchObject(testPayload);
  });
}); 