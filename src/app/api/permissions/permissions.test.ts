import { GET } from './route';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret';

// Types for test responses
interface PermissionResponse {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// Test data
const testUser = {
  name: 'Test Admin',
  email: '<EMAIL>',
  phoneNumber: '1234567890',
  passwordHash: 'hashedPassword',
  isActive: true,
};

const testPermissions = [
  { name: 'view_candidates' },
  { name: 'edit_candidates' },
  { name: 'delete_candidates' },
  { name: 'view_permissions' },
  { name: 'edit_permissions' },
];

let userId: number;
let roleId: number;
const permissionIds: number[] = [];

beforeAll(async () => {
  // Clean up any existing test data (delete dependents first)
  await prisma.user.deleteMany({ where: { email: testUser.email } });
  await prisma.userRole.deleteMany({});
  await prisma.rolePermission.deleteMany({});
  await prisma.permission.deleteMany({ where: { name: { in: testPermissions.map(p => p.name) } } });
  await prisma.role.deleteMany({ where: { name: 'test_admin' } });

  // Create test user
  const user = await prisma.user.create({ data: testUser });
  userId = user.id;

  // Create test permissions
  for (const perm of testPermissions) {
    const permission = await prisma.permission.create({
      data: {
        name: perm.name,
        createdAt: new Date(),
        createdBy: userId,
        updatedAt: new Date(),
        updatedBy: userId,
      },
    });
    permissionIds.push(permission.id);
  }

  // Create test role with view_permissions permission
  const role = await prisma.role.create({
    data: {
      name: 'test_admin',
      createdAt: new Date(),
      createdBy: userId,
      updatedAt: new Date(),
      updatedBy: userId,
    },
  });
  roleId = role.id;

  // Assign view_permissions to role
  const viewPermissionsId = permissionIds[3]; // view_permissions
  await prisma.rolePermission.create({
    data: {
      roleId,
      permissionId: viewPermissionsId,
      createdAt: new Date(),
      createdBy: userId,
      updatedAt: new Date(),
      updatedBy: userId,
    },
  });

  // Assign role to user
  await prisma.userRole.create({
    data: {
      userId,
      roleId,
      createdAt: new Date(),
      createdBy: userId,
      updatedAt: new Date(),
      updatedBy: userId,
    },
  });
});

afterAll(async () => {
  // Clean up test data (delete dependents first)
  await prisma.userRole.deleteMany({ where: { userId } });
  await prisma.rolePermission.deleteMany({ where: { roleId } });
  await prisma.role.deleteMany({ where: { id: roleId } });
  await prisma.permission.deleteMany({ where: { id: { in: permissionIds } } });
  await prisma.user.deleteMany({ where: { id: userId } });
  await prisma.$disconnect();
});

// Helper function to create valid JWT token
function createValidToken(permissions = ['view_permissions']) {
  return jwt.sign(
    {
      id: userId,
      name: testUser.name,
      email: testUser.email,
      roles: ['test_admin'],
      permissions,
    },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
}

describe('/api/permissions', () => {
  describe('Authentication Tests', () => {
    it('should return 401 if no token cookie is present', async () => {
      const req = new Request('http://localhost/api/permissions', { method: 'GET' });
      const res = await GET(req);
      expect(res.status).toBe(401);
      const body = await res.json();
      expect(body.error).toBe('Authentication required');
    });

    it('should return 401 if token is invalid', async () => {
      const req = new Request('http://localhost/api/permissions', {
        method: 'GET',
        headers: { cookie: 'token=invalidtoken' },
      });
      const res = await GET(req);
      expect(res.status).toBe(401);
      const body = await res.json();
      expect(body.error).toBe('Invalid or expired token');
    });

    it('should return 401 if user lacks view_permissions permission', async () => {
      const tokenWithoutPermission = createValidToken(['other_permission']);
      const req = new Request('http://localhost/api/permissions', {
        method: 'GET',
        headers: { cookie: `token=${tokenWithoutPermission}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(401);
      const body = await res.json();
      expect(body.error).toBe('Insufficient permissions');
    });
  });

  describe('Success Cases', () => {
    it('should return permissions with default pagination', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      expect(Array.isArray(body.data.permissions)).toBe(true);
      expect(body.data.permissions.length).toBeGreaterThan(0);
      expect(body.data.pagination).toMatchObject({
        page: 1,
        limit: 10,
        total: expect.any(Number),
        totalPages: expect.any(Number),
        hasNext: expect.any(Boolean),
        hasPrev: false,
      });
    });

    it('should filter permissions by name', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?name=view', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      body.data.permissions.forEach((perm: PermissionResponse) => {
        expect(perm.name.toLowerCase()).toContain('view');
      });
    });

    it('should sort permissions by name ascending', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?sort=name&order=asc', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      const names = (body.data.permissions as PermissionResponse[]).map((p) => p.name);
      const sortedNames = [...names].sort();
      expect(names).toEqual(sortedNames);
    });

    it('should sort permissions by name descending', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?sort=name&order=desc', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      const names = (body.data.permissions as PermissionResponse[]).map((p) => p.name);
      const sortedNames = [...names].sort().reverse();
      expect(names).toEqual(sortedNames);
    });

    it('should handle pagination correctly', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?page=1&limit=2', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      expect(body.data.permissions.length).toBeLessThanOrEqual(2);
      expect(body.data.pagination.page).toBe(1);
      expect(body.data.pagination.limit).toBe(2);
    });
  });

  describe('Validation Tests', () => {
    it('should return 400 for invalid page parameter', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?page=0', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.error).toBe('Invalid query parameters');
    });

    it('should return 400 for invalid limit parameter', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?limit=101', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.error).toBe('Invalid query parameters');
    });

    it('should return 400 for invalid sort parameter', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?sort=invalidfield', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.error).toBe('Invalid query parameters');
    });
  });

  describe('Edge Cases', () => {
    it('should return empty results for non-matching filter', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?name=nonexistent', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      expect(body.data.permissions).toEqual([]);
      expect(body.data.pagination.total).toBe(0);
    });

    it('should handle large page numbers gracefully', async () => {
      const token = createValidToken();
      const req = new Request('http://localhost/api/permissions?page=9999', {
        method: 'GET',
        headers: { cookie: `token=${token}` },
      });
      const res = await GET(req);
      expect(res.status).toBe(200);
      const body = await res.json();
      
      expect(body.success).toBe(true);
      expect(body.data.permissions).toEqual([]);
    });
  });
}); 