import { NextResponse } from 'next/server';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

// Validation schema for query parameters
const PermissionsQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  name: z.string().optional(),
  sort: z.enum(['id', 'name', 'createdAt', 'updatedAt']).default('name'),
  order: z.enum(['asc', 'desc']).default('asc'),
});

export async function GET(req: Request) {
  try {
    // Authentication and authorization
    const auth = await authenticateAndAuthorize(req, 'view_permissions');
    if (!auth.success) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const qp = (key: string) => {
      const v = url.searchParams.get(key);
      return v === null ? undefined : v;
    };
    const queryParams = {
      page: qp('page'),
      limit: qp('limit'),
      name: qp('name'),
      sort: qp('sort'),
      order: qp('order'),
    };

    const validationResult = PermissionsQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error.errors, 'Query:', queryParams);
      return NextResponse.json(
        { error: 'Invalid query parameters', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { page, limit, name, sort, order } = validationResult.data;

    // Build database query conditions
    const whereClause: Record<string, unknown> = {
      deletedAt: null, // Exclude soft-deleted records
    };

    if (name) {
      whereClause.name = {
        contains: name,
      };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute database queries
    const [permissions, totalCount] = await Promise.all([
      prisma.permission.findMany({
        where: whereClause,
        orderBy: {
          [sort]: order,
        },
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          createdAt: true,
          updatedAt: true,
        },
      }),
      prisma.permission.count({
        where: whereClause,
      }),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    // Format response
    const response = {
      success: true,
      data: {
        permissions,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages,
          hasNext,
          hasPrev,
        },
      },
    };

    return NextResponse.json(response);
  } catch (err) {
    console.error('Permissions API error:', err);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 