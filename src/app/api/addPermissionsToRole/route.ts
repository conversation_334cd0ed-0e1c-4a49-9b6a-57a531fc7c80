import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

import { NextResponse } from 'next/server';

import { getIpFromRequest } from '@/lib/utils';
import { logAudit } from '@/lib/utils/auditLog';
import { authenticateAndAuthorize } from '@/lib/utils/auth';

const prisma = new PrismaClient();

// Validation schema
const AddPermissionsToRoleSchema = z.object({
  roleId: z.coerce.number().int().min(1),
  permissionIds: z.array(z.coerce.number().int().min(1)).min(1),
});

export async function POST(request: Request) {
  // Auth
  const auth = await authenticateAndAuthorize(request, 'manage_roles');
  if (!auth.success) {
    return NextResponse.json({ success: false, error: auth.error }, { status: 401 });
  }

  // Parse body
  let body;
  try {
    body = await request.json();
  } catch {
    return NextResponse.json({ success: false, error: 'Invalid JSON body' }, { status: 400 });
  }
  const parse = AddPermissionsToRoleSchema.safeParse(body);
  if (!parse.success) {
    return NextResponse.json({ success: false, error: parse.error.flatten() }, { status: 400 });
  }
  const { roleId, permissionIds } = parse.data;

  // Check role exists and not deleted
  const role = await prisma.role.findUnique({ where: { id: roleId, deletedAt: null } });
  if (!role) {
    return NextResponse.json({ success: false, error: 'Role not found' }, { status: 400 });
  }

  // Check all permissions exist and not deleted
  const permissions = await prisma.permission.findMany({
    where: { id: { in: permissionIds }, deletedAt: null },
    select: { id: true },
  });
  const foundIds = permissions.map((p) => p.id);
  const missing = permissionIds.filter((id) => !foundIds.includes(id));
  if (missing.length > 0) {
    return NextResponse.json(
      { success: false, error: `Permissions not found: ${missing.join(', ')}` },
      { status: 400 },
    );
  }

  // Find already existing role-permission associations
  const existing = await prisma.rolePermission.findMany({
    where: {
      roleId,
      permissionId: { in: permissionIds },
      deletedAt: null,
    },
    select: { permissionId: true },
  });
  const alreadyLinked = new Set(existing.map((rp) => rp.permissionId));
  const toAdd = permissionIds.filter((id) => !alreadyLinked.has(id));

  // Create new RolePermission entries for those not already linked
  const rolePermissionIds = await prisma.$transaction(
    toAdd.map((permissionId) =>
      prisma.rolePermission.create({
        data: { roleId, permissionId },
      }),
    ),
  );

  // Get total permissions for the role (excluding soft-deleted)
  const totalPermissions = await prisma.rolePermission.count({
    where: { roleId, deletedAt: null },
  });

  const ip = getIpFromRequest(request);

  await logAudit({
    prisma,
    action: 'ADD_PERMISSION_TO_ROLE',
    tableName: 'RolePermission',
    recordId: rolePermissionIds.map(({ id }) => id).toString(),
    userId: auth.userId,
    newValue: toAdd,
    metadata: JSON.stringify({ ...parse.data, ip }),
  });

  return NextResponse.json({
    success: true,
    data: {
      roleId,
      addedPermissionIds: toAdd,
      totalPermissions,
    },
  });
}
