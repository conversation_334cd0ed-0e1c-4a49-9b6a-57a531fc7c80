"use client";

import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Sheet } from "@/components/common/Sheet";
import { Input } from "@/components/ui/input";
import { LocationTable } from "@/components/location/LocationTable";
import { LocationForm } from "@/components/location/LocationForm";
import type { Location, Pagination, LocationType } from "@/types/location";
import { toast } from "sonner";
import { AlertDialog } from "@/components/common/AlertDialog";

const defaultForm: Omit<Location, "id"> = {
  name: "",
  circle: "",
  address: "",
  type: "ACADEMY",
};

export default function LocationsPage() {
  const [locations, setLocations] = useState<Location[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  });
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formState, setFormState] = useState({
    open: false,
    mode: "create" as "create" | "edit",
    initial: defaultForm,
    editId: null as number | null,
    error: null as string | null,
    loading: false,
  });
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    target: null as Location | null,
  });

  // Fetch locations
  const fetchLocations = async (opts?: { page?: number; search?: string }) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: String(opts?.page ?? pagination.page),
        limit: String(pagination.limit),
      });
      if (opts?.search ?? search) params.append("name", opts?.search ?? search);
      const res = await fetch(`/api/locations?${params.toString()}`);
      const data = await res.json();
      if (!data.success)
        throw new Error(data.error || "Failed to fetch locations");
      setLocations(data.data.locations);
      setPagination(data.data.pagination);
    } catch (e: any) {
      setError(e.message || "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLocations({ page: 1 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handlers
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchLocations({ page: 1, search });
  };

  const openCreate = () => {
    setFormState((prev) => ({
      ...prev,
      open: true,
      mode: "create",
      initial: defaultForm,
      editId: null,
      error: null,
      loading: false,
    }));
  };

  const openEdit = (loc: Location) => {
    setFormState((prev) => ({
      ...prev,
      open: true,
      mode: "edit",
      initial: {
        name: loc.name,
        circle: loc.circle,
        address: loc.address,
        type: loc.type as LocationType,
      },
      editId: loc.id,
      error: null,
      loading: false,
    }));
  };

  const closeSheet = () => {
    setFormState((prev) => ({
      ...prev,
      open: false,
      editId: null,
      initial: defaultForm,
      error: null,
      loading: false,
    }));
  };

  const handleFormSubmit = async (values: typeof defaultForm) => {
    setFormState((prev) => ({ ...prev, loading: true, error: null }));
    try {
      const method =
        formState.mode === "edit" && formState.editId ? "PUT" : "POST";
      const url =
        formState.mode === "edit" && formState.editId
          ? `/api/locations/${formState.editId}`
          : "/api/locations";
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const data = await res.json();
      if (!data.success) {
        console.log(data.error);
        toast.error(data.error || "Failed to save location");
        setFormState((prev) => ({ ...prev, loading: false }));
        return;
      }
      toast.success(
        formState.mode === "edit"
          ? "Location updated successfully"
          : "Location created successfully"
      );
      closeSheet();
      fetchLocations({ page: 1 });
    } catch (e: any) {
      toast.error(e.message || "Unknown error");
      setFormState((prev) => ({ ...prev, loading: false }));
    }
  };

  const handleDeleteClick = (loc: Location) => {
    setDeleteDialog({ open: true, target: loc });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.target) return;
    try {
      const res = await fetch(`/api/locations/${deleteDialog.target.id}`, {
        method: "DELETE",
      });
      const data = await res.json();
      if (!data.success) throw new Error(data.error || "Failed to delete");
      toast.success("Location deleted successfully");
      fetchLocations({ page: 1 });
    } catch (e: any) {
      toast.error(e.message || "Unknown error");
    } finally {
      setDeleteDialog({ open: false, target: null });
    }
  };

  // Pagination
  const gotoPage = (page: number) => {
    fetchLocations({ page });
  };

  return (
    <div className="max-w-5xl mx-auto p-4">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">Location Management</h1>
        <div className="flex gap-2">
          <Button className="cursor-pointer" onClick={openCreate}>
            Add Location
          </Button>
          <Sheet
            open={formState.open}
            onOpenChange={(open) => setFormState((prev) => ({ ...prev, open }))}
            title={formState.mode === "edit" ? "Edit Location" : "Add Location"}
            description={
              formState.mode === "edit"
                ? "Update location details."
                : "Create a new location."
            }
            onClose={closeSheet}
          >
            <LocationForm
              initialValues={formState.initial}
              onSubmit={handleFormSubmit}
              onCancel={closeSheet}
              loading={formState.loading}
              error={formState.error}
            />
          </Sheet>
        </div>
      </div>
      <form onSubmit={handleSearch} className="flex gap-2 mb-4">
        <Input
          placeholder="Search by name..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-xs"
        />
        <Button className="cursor-pointer" type="submit" variant="outline">
          Search
        </Button>
      </form>
      <LocationTable
        locations={locations}
        loading={loading}
        error={error}
        pagination={pagination}
        onEdit={openEdit}
        onDelete={handleDeleteClick}
        onPageChange={gotoPage}
      />
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog((prev) => ({ ...prev, open }))}
        title="Delete Location"
        description={
          deleteDialog.target ? (
            <>
              Are you sure you want to delete <b>{deleteDialog.target.name}</b>?
              This action cannot be undone.
            </>
          ) : (
            "No location selected."
          )
        }
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteDialog({ open: false, target: null })}
      />
    </div>
  );
}
