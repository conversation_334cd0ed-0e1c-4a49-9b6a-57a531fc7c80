"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sheet } from "@/components/common/Sheet";
import { Input } from "@/components/ui/input";
import { HolidayTable } from "@/components/holiday/HolidayTable";
import { HolidayForm } from "@/components/holiday/HolidayForm";
import { LocationSelect } from "@/components/holiday/LocationSelect";
import type { Holiday, HolidayForm as HolidayFormType } from "@/types/holiday";
import type { Location, Pagination } from "@/types/location";
import { toast } from "sonner";
import { AlertDialog } from "@/components/common/AlertDialog";

const defaultForm: HolidayFormType = { date: "", reason: "", locationId: 0 };

export default function HolidaysPage() {
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [pagination, setPagination] = useState<Pagination>({ page: 1, limit: 10, total: 0, totalPages: 1, hasNext: false, hasPrev: false });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formState, setFormState] = useState({
    open: false,
    mode: "create" as "create" | "edit",
    initial: defaultForm,
    editId: null as number | null,
    error: null as string | null,
    loading: false,
  });
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    target: null as Holiday | null,
  });
  const [filterLocationId, setFilterLocationId] = useState<number | null>(null);

  // Fetch locations
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const res = await fetch("/api/locations?page=1&limit=100");
        const data = await res.json();
        if (data.success) setLocations(data.data.locations);
      } catch {}
    };
    fetchLocations();
  }, []);

  // Fetch holidays
  const fetchHolidays = async (opts?: { page?: number; locationId?: number | null }) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: String(opts?.page ?? pagination.page),
        limit: String(pagination.limit),
      });
      const locationId = opts?.locationId ?? filterLocationId;
      if (locationId != null && locationId > 0) {
        params.append("locationId", String(locationId));
      }
      const res = await fetch(`/api/holidays?${params.toString()}`);
      const data = await res.json();
      if (!data.success) throw new Error(data.error || "Failed to fetch holidays");
      setHolidays(data.data);
      setPagination((prev) => ({ ...prev, ...{
        page: opts?.page ?? pagination.page,
        total: data.data.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      }}));
    } catch (e: any) {
      setError(e.message || "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHolidays({ page: 1 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterLocationId]);

  // Handlers
  const openCreate = () => {
    setFormState((prev) => ({
      ...prev,
      open: true,
      mode: "create",
      initial: defaultForm,
      editId: null,
      error: null,
      loading: false,
    }));
  };

  const openEdit = (holiday: Holiday) => {
    setFormState((prev) => ({
      ...prev,
      open: true,
      mode: "edit",
      initial: { date: holiday.date.slice(0, 10), reason: holiday.reason, locationId: holiday.locationId },
      editId: holiday.id,
      error: null,
      loading: false,
    }));
  };

  const closeSheet = () => {
    setFormState((prev) => ({
      ...prev,
      open: false,
      editId: null,
      initial: defaultForm,
      error: null,
      loading: false,
    }));
  };

  const handleFormSubmit = async (values: HolidayFormType) => {
    setFormState((prev) => ({ ...prev, loading: true, error: null }));
    try {
      const method = formState.mode === "edit" && formState.editId ? "PUT" : "POST";
      const url = formState.mode === "edit" && formState.editId ? `/api/holidays/${formState.editId}` : "/api/holidays";
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const data = await res.json();
      if (!data.success) {
        toast.error(data.error || "Failed to save holiday");
        setFormState((prev) => ({ ...prev, loading: false }));
        return;
      }
      toast.success(formState.mode === "edit" ? "Holiday updated successfully" : "Holiday created successfully");
      closeSheet();
      fetchHolidays({ page: 1 });
    } catch (e: any) {
      toast.error(e.message || "Unknown error");
      setFormState((prev) => ({ ...prev, loading: false }));
    }
  };

  const handleDeleteClick = (holiday: Holiday) => {
    setDeleteDialog({ open: true, target: holiday });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.target) return;
    try {
      const res = await fetch(`/api/holidays/${deleteDialog.target.id}`, { method: "DELETE" });
      const data = await res.json();
      if (!data.success) throw new Error(data.error || "Failed to delete");
      toast.success("Holiday deleted successfully");
      fetchHolidays({ page: 1 });
    } catch (e: any) {
      toast.error(e.message || "Unknown error");
    } finally {
      setDeleteDialog({ open: false, target: null });
    }
  };

  // Pagination
  const gotoPage = (page: number) => {
    fetchHolidays({ page });
  };

  return (
    <div className="max-w-5xl mx-auto p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
        <h1 className="text-2xl font-bold">Holiday Management</h1>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <LocationSelect
            value={filterLocationId}
            onChange={(id) => { setFilterLocationId(id); fetchHolidays({ page: 1, locationId: id }); }}
            locations={locations || []}
            placeholder={locations.length === 0 ? "No locations available" : "Filter by location"}
            disabled={locations.length === 0}
          />
          <Button className="cursor-pointer" onClick={openCreate}>Add Holiday</Button>
          <Sheet
            open={formState.open}
            onOpenChange={(open) => setFormState((prev) => ({ ...prev, open }))}
            title={formState.mode === "edit" ? "Edit Holiday" : "Add Holiday"}
            description={formState.mode === "edit" ? "Update holiday details." : "Create a new holiday."}
            onClose={closeSheet}
          >
            <HolidayForm
              initialValues={formState.initial}
              onSubmit={handleFormSubmit}
              onCancel={closeSheet}
              loading={formState.loading}
              error={formState.error}
              locations={locations}
            />
          </Sheet>
        </div>
      </div>
      <HolidayTable
        holidays={holidays}
        loading={loading}
        error={error}
        pagination={pagination}
        onEdit={openEdit}
        onDelete={handleDeleteClick}
        onPageChange={gotoPage}
      />
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog((prev) => ({ ...prev, open }))}
        title="Delete Holiday"
        description={deleteDialog.target ? (
          <>Are you sure you want to delete the holiday on <b>{deleteDialog.target.date.slice(0, 10)}</b>? This action cannot be undone.</>
        ) : "No holiday selected."}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteDialog({ open: false, target: null })}
      />
    </div>
  );
} 