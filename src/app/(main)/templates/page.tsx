"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { AlertDialog } from "@/components/common/AlertDialog";
import { TemplateTable } from "@/components/template/TemplateTable";
import type { Template } from "@/hooks/useTemplates";
import type { Pagination } from "@/types/location";

const defaultPagination: Pagination = {
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 1,
  hasNext: false,
  hasPrev: false,
};

export default function TemplateListPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [pagination, setPagination] = useState<Pagination>(defaultPagination);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    target: null as Template | null,
  });

  // Fetch templates with pagination
  const fetchTemplates = async (page = 1) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: String(page),
        limit: String(pagination.limit),
      });
      const res = await fetch(`/api/templates?${params.toString()}`);
      const data = await res.json();
      if (!data.success)
        throw new Error(data.error || "Failed to fetch templates");
      setTemplates(data.data.templates || data.data); // support both {templates, pagination} and array
      setPagination(
        data.data.pagination || {
          page: page,
          limit: pagination.limit,
          total:
            data.data.total ||
            (data.data.templates
              ? data.data.templates.length
              : data.data.length),
          totalPages: data.data.totalPages || 1,
          hasNext: data.data.hasNext || false,
          hasPrev: data.data.hasPrev || false,
        }
      );
    } catch (err: any) {
      setError(err.message || "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleDeleteClick = (template: Template) => {
    setDeleteDialog({ open: true, target: template });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.target) return;
    const { id, name } = deleteDialog.target;
    setLoading(true);
    try {
      const res = await fetch(`/api/templates/${id}`, { method: "DELETE" });
      const data = await res.json();
      if (!data.success)
        throw new Error(data.error || "Failed to delete template");
      toast.success(`Template "${name}" deleted successfully`);
      fetchTemplates(pagination.page);
    } catch (err: any) {
      toast.error(err.message || "Unknown error");
    } finally {
      setLoading(false);
      setDeleteDialog({ open: false, target: null });
    }
  };

  const handlePageChange = (page: number) => {
    fetchTemplates(page);
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Templates</h1>
        <Link href="/templates/new">
          <Button variant="default" type="button">
            Create Template
          </Button>
        </Link>
      </div>
      <div className="overflow-x-auto">
        <TemplateTable
          templates={templates}
          loading={loading}
          error={error}
          pagination={pagination}
          onEdit={() => {}}
          onDelete={handleDeleteClick}
          onPageChange={handlePageChange}
        />
      </div>
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog((prev) => ({ ...prev, open }))}
        title="Delete Template"
        description={
          deleteDialog.target ? (
            <>
              Are you sure you want to delete <b>{deleteDialog.target.name}</b>?
              This marks it as inactive.
            </>
          ) : (
            "No template selected."
          )
        }
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteDialog({ open: false, target: null })}
      />
    </div>
  );
}
