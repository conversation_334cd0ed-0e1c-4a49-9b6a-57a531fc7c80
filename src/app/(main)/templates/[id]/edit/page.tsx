"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { TemplateForm } from "@/components/template/TemplateForm";
import { useCreateTemplateForm, TemplateFormValues } from "@/hooks/useCreateTemplateForm";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { DaySchedule } from "@/components/template/ScheduleTable";

const emptyDay: DaySchedule = { nature: "CRT", modules: [{ moduleId: 0, startTime: "", endTime: "" }] };
const emptySchedule: DaySchedule[] = Array.from({ length: 30 }, () => ({ ...emptyDay }));

function buildScheduleArray(
  schedule: Array<{ dayNumber: number; groupType: string; nature: "CRT" | "OJT"; modules: Array<{ moduleId: number; startTime: string; endTime: string }> }>,
  groupType: string
): DaySchedule[] {
  return Array.from({ length: 30 }, (_, i) => {
    const day = schedule.find(
      (d) => d.groupType === groupType && d.dayNumber === i + 1
    );
    if (day) {
      return {
        nature: day.nature,
        modules:
          day.nature === "CRT"
            ? day.modules.map((m) => ({
                moduleId: m.moduleId || 0,
                startTime: m.startTime || "",
                endTime: m.endTime || "",
              }))
            : [],
      };
    } else {
      return { ...emptyDay };
    }
  });
}

export default function EditTemplatePage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialValues, setInitialValues] = useState<Partial<TemplateFormValues>>();
  const [submitting, setSubmitting] = useState(false);

  // Fetch template data
  useEffect(() => {
    async function fetchTemplate() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch(`/api/templates/${id}`);
        const data = await res.json();
        if (!data.success) throw new Error(data.error || "Failed to fetch template");
        const t = data.data;
        let scheduleSA: DaySchedule[] = emptySchedule;
        let scheduleSM: DaySchedule[] = emptySchedule;
        let scheduleOPTOM: DaySchedule[] = emptySchedule;
        if (t.batchType === "SA_SM") {
          scheduleSA = buildScheduleArray(t.schedule, "SA");
          scheduleSM = buildScheduleArray(t.schedule, "SM");
        } else if (t.batchType === "OPTOM") {
          scheduleOPTOM = buildScheduleArray(t.schedule, "ALL");
        }
        setInitialValues({
          name: t.name,
          description: t.description || "",
          batchType: t.batchType,
          scheduleSA,
          scheduleSM,
          scheduleOPTOM,
        });
      } catch (e: any) {
        setError(e.message || "Unknown error");
      } finally {
        setLoading(false);
      }
    }
    fetchTemplate();
  }, [id]);

  // Always call the hook at the top level
  const form = useCreateTemplateForm(initialValues);

  // Update handler
  async function handleUpdate() {
    form.setFormError("");
    form.setNameTouched(true);
    form.setBatchTypeTouched(true);
    if (!form.isFormValid) return;
    setSubmitting(true);
    try {
      const res = await fetch(`/api/templates/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          name: form.name,
          description: form.description,
          batchType: form.batchType,
          schedule: (() => {
            if (form.batchType === "SA_SM") {
              return [
                ...form.scheduleSA.map((day, idx) => ({ ...day, dayNumber: idx + 1, groupType: "SA" })),
                ...form.scheduleSM.map((day, idx) => ({ ...day, dayNumber: idx + 1, groupType: "SM" })),
              ];
            } else if (form.batchType === "OPTOM") {
              return form.scheduleOPTOM.map((day, idx) => ({ ...day, dayNumber: idx + 1, groupType: "ALL" }));
            }
            return [];
          })(),
        }),
      });
      const data = await res.json();
      if (data.success) {
        toast.success("Template updated successfully!");
        router.push("/templates");
      } else {
        toast.error(data.error || "Failed to update template");
      }
    } catch (err) {
      toast.error("Failed to update template");
    } finally {
      setSubmitting(false);
    }
  }

  return (
    <div className="max-w-5xl mx-auto p-4">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">Edit Template</h1>
        <Link href="/templates">
          <Button variant="outline" type="button">Back to List</Button>
        </Link>
      </div>
      {loading ? (
        <div className="p-4 text-center">Loading...</div>
      ) : error ? (
        <div className="p-4 text-destructive text-center">{error}</div>
      ) : initialValues ? (
        <TemplateForm
          key={id}
          {...form}
          onSubmit={handleUpdate}
          submitText={submitting ? "Updating..." : "Update"}
          cancelHref="/templates"
        />
      ) : (
        <div className="p-4 text-center">Loading form...</div>
      )}
    </div>
  );
} 