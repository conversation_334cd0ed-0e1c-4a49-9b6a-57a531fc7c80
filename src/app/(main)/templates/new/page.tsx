"use client";

import { TemplateForm } from "@/components/template/TemplateForm";
import { useCreateTemplateForm } from "@/hooks/useCreateTemplateForm";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CreateTemplatePage() {
  const form = useCreateTemplateForm();
  return (
    <div className="max-w-5xl mx-auto p-4">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">Create Template</h1>
        <Link href="/templates">
          <Button variant="outline" type="button">Back to List</Button>
        </Link>
      </div>
      <TemplateForm
        {...form}
        onSubmit={form.handleSubmit}
        submitText="Save"
        cancelHref="/templates"
      />
    </div>
  );
} 