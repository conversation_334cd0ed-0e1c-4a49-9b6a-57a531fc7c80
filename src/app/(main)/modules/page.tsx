"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet } from "@/components/common/Sheet";
import { Input } from "@/components/ui/input";
import { ModuleTable } from "@/components/module/ModuleTable";
import { ModuleFormComponent } from "@/components/module/ModuleForm";
import type { Module, ModuleForm } from "@/types/module";
import type { Pagination } from "@/types/location";
import { toast } from "sonner";
import { AlertDialog } from "@/components/common/AlertDialog";

const defaultForm: ModuleForm = { name: "", description: "", duration: 1, nature: "CRT", program: "SA" };

export default function ModulesPage() {
  const [modules, setModules] = useState<Module[]>([]);
  const [pagination, setPagination] = useState<Pagination>({ page: 1, limit: 10, total: 0, totalPages: 1, hasNext: false, hasPrev: false });
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formState, setFormState] = useState({
    open: false,
    mode: "create" as "create" | "edit",
    initial: defaultForm,
    editId: null as number | null,
    error: null as string | null,
    loading: false,
  });
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    target: null as Module | null,
  });

  // Fetch modules
  const fetchModules = async (opts?: { page?: number; search?: string }) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: String(opts?.page ?? pagination.page),
        limit: String(pagination.limit),
      });
      if (opts?.search ?? search) params.append("name", opts?.search ?? search);
      const res = await fetch(`/api/modules?${params.toString()}`);
      const data = await res.json();
      if (!data.success) throw new Error(data.error || "Failed to fetch modules");
      setModules(data.data.modules);
      setPagination(data.data.pagination);
    } catch (e: any) {
      setError(e.message || "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModules({ page: 1 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handlers
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchModules({ page: 1, search });
  };

  const openCreate = () => {
    setFormState((prev) => ({
      ...prev,
      open: true,
      mode: "create",
      initial: defaultForm,
      editId: null,
      error: null,
      loading: false,
    }));
  };

  const openEdit = (mod: Module) => {
    setFormState((prev) => ({
      ...prev,
      open: true,
      mode: "edit",
      initial: { name: mod.name, description: mod.description, duration: mod.duration, nature: mod.nature, program: mod.program },
      editId: mod.id,
      error: null,
      loading: false,
    }));
  };

  const closeSheet = () => {
    setFormState((prev) => ({
      ...prev,
      open: false,
      editId: null,
      initial: defaultForm,
      error: null,
      loading: false,
    }));
  };

  const handleFormSubmit = async (values: ModuleForm) => {
    setFormState((prev) => ({ ...prev, loading: true, error: null }));
    try {
      const method = formState.mode === "edit" && formState.editId ? "PUT" : "POST";
      const url = formState.mode === "edit" && formState.editId ? `/api/modules/${formState.editId}` : "/api/modules";
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const data = await res.json();
      if (!data.success) {
        toast.error(data.error || "Failed to save module");
        setFormState((prev) => ({ ...prev, loading: false }));
        return;
      }
      toast.success(formState.mode === "edit" ? "Module updated successfully" : "Module created successfully");
      closeSheet();
      fetchModules({ page: 1 });
    } catch (e: any) {
      toast.error(e.message || "Unknown error");
      setFormState((prev) => ({ ...prev, loading: false }));
    }
  };

  const handleDeleteClick = (mod: Module) => {
    setDeleteDialog({ open: true, target: mod });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.target) return;
    try {
      const res = await fetch(`/api/modules/${deleteDialog.target.id}`, { method: "DELETE" });
      const data = await res.json();
      if (!data.success) throw new Error(data.error || "Failed to delete");
      toast.success("Module deleted successfully");
      fetchModules({ page: 1 });
    } catch (e: any) {
      toast.error(e.message || "Unknown error");
    } finally {
      setDeleteDialog({ open: false, target: null });
    }
  };

  // Pagination
  const gotoPage = (page: number) => {
    fetchModules({ page });
  };

  return (
    <div className="max-w-5xl mx-auto p-4">
      <div className="flex items-center justify-between mb-4 gap-2 flex-col sm:flex-row">
        <h1 className="text-2xl font-bold">Module Management</h1>
        <div className="flex gap-2">
          <Button className="cursor-pointer" onClick={openCreate}>Add Module</Button>
          <Sheet
            open={formState.open}
            onOpenChange={(open) => setFormState((prev) => ({ ...prev, open }))}
            title={formState.mode === "edit" ? "Edit Module" : "Add Module"}
            description={formState.mode === "edit" ? "Update module details." : "Create a new module."}
            onClose={closeSheet}
          >
            <ModuleFormComponent
              initialValues={formState.initial}
              onSubmit={handleFormSubmit}
              onCancel={closeSheet}
              loading={formState.loading}
              error={formState.error}
            />
          </Sheet>
        </div>
      </div>
      <form onSubmit={handleSearch} className="flex gap-2 mb-4">
        <Input
          placeholder="Search by name..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-xs"
        />
        <Button className="cursor-pointer" type="submit" variant="outline">Search</Button>
      </form>
      <ModuleTable
        modules={modules}
        loading={loading}
        error={error}
        pagination={pagination}
        onEdit={openEdit}
        onDelete={handleDeleteClick}
        onPageChange={gotoPage}
      />
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog((prev) => ({ ...prev, open }))}
        title="Delete Module"
        description={deleteDialog.target ? (
          <>Are you sure you want to delete <b>{deleteDialog.target.name}</b>? This action cannot be undone.</>
        ) : "No module selected."}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteDialog({ open: false, target: null })}
      />
    </div>
  );
} 