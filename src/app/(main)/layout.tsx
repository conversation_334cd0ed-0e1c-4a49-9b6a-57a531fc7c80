import { AppSidebar } from '@/components/app-sidebar';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import UserMenu from '@/components/menu-popup';
import { Separator } from '@/components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="sticky top-0 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4">
            <SidebarTrigger className="-ml-1 scale-125 cursor-pointer" />
            <Separator className="mr-2 h-4" orientation="vertical" />
            <UserMenu className="absolute right-6 bottom-4 cursor-pointer" />
          </header>
          <main className="p-4">{children}</main>
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
