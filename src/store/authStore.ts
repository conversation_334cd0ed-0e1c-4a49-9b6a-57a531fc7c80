// stores/authStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { AuthMe } from '@/types/user.type';

interface AuthState {
  user: AuthMe | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  setUser: (user: AuthMe | null) => void;
  setLoading: (loading: boolean) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    (set) => ({
      user: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true for initial auth check
      setUser: (user) =>
        set({
          user,
          isAuthenticated: !!user,
          isLoading: false,
        }),
      setLoading: (loading) => set({ isLoading: loading }),
      logout: () =>
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        }),
    }),
    { name: 'authStore' },
  ),
);
