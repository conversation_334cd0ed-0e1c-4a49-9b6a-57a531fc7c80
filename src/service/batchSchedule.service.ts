import { ScheduleTemplate, Holiday } from '@prisma/client';
import { PrismaClient, BatchType, $Enums, Roaster, Batch, BatchModule } from '@prisma/client';

/**
 * Represents a single entry in the generated schedule.
 */
export type ScheduleEntry = {
  day: number;
  date: string;
  groupType: string | null;
  module: string | null;
  moduleId?: number;
  isHoliday: boolean;
};

/**
 * Type for a trainer with modules and availability, as returned by the trainersRaw query.
 */
export type TrainerWithModulesAndAvailability = {
  id: number;
  user: { name: string; email: string };
  trainerModules: { module: { id: number; name: string; duration?: number } }[];
  availability: { availableDate: Date; isAvailable: boolean }[];
};

/**
 * Returns a set of holiday date strings (YYYY-MM-DD) for quick lookup.
 */
function getHolidayDateSet(holidays: Holiday[]): Set<string> {
  return new Set(holidays.map((h) => h.date.toISOString().slice(0, 10)));
}

/**
 * Returns all schedule templates for a given day number.
 */
function getDayTemplates(
  scheduleTemplates: (ScheduleTemplate & {
    module: { id: number; name: string; duration?: number };
  })[],
  dayNumber: number,
) {
  return scheduleTemplates.filter((t) => t.dayNumber === dayNumber);
}

/**
 * Sorts trainers by utilization (ascending).
 */
function sortTrainersByUtilization(
  trainers: TrainerWithModulesAndAvailability[],
  trainerUtilization: Record<number, number>,
) {
  trainers.sort((a, b) => (trainerUtilization[a.id] || 0) - (trainerUtilization[b.id] || 0));
}

/**
 * Generates a flat schedule array for a batch, including holidays.
 *
 * Schedule Generation Rules:
 * - For each day, starting from the given startDate, check if it is a holiday.
 * - If it is a holiday, add a schedule entry with isHoliday=true and no module/groupType.
 * - If not a holiday, add one entry per (groupType, module) for that day, as defined in the schedule templates.
 * - The day number increments for every calendar day (including holidays).
 * - The output is a flat list, with one object per (day, date, groupType, module) combination, or a holiday.
 *
 * @param scheduleTemplates Array of schedule templates (with module relation included)
 * @param holidays Array of holidays for the location
 * @param startDate The start date for the schedule
 * @returns Array of schedule entries, one per (day, date, groupType, module) or holiday
 */
export function generateSchedule(
  scheduleTemplates: (ScheduleTemplate & {
    module: { id: number; name: string; duration?: number };
  })[],
  holidays: Holiday[],
  startDate: Date,
): ScheduleEntry[] {
  // Convert holidays to a set of date strings for fast lookup
  const holidayDates = getHolidayDateSet(holidays);
  // The final schedule array to return
  const schedule: ScheduleEntry[] = [];
  // Start from the given start date
  const currentDate = new Date(startDate);
  let templateIdx = 0; // Index into scheduleTemplates
  let day = 1; // Day number in the schedule (increments for every calendar day)

  // Continue until all schedule templates have been processed
  while (templateIdx < scheduleTemplates.length) {
    const dateStr = currentDate.toISOString().slice(0, 10); // Format date as YYYY-MM-DD
    // Treat Sundays as holidays in addition to the provided holidays
    if (holidayDates.has(dateStr) || currentDate.getDay() === 0) {
      // If the current date is a holiday or Sunday, add a holiday entry
      schedule.push({ day, date: dateStr, groupType: null, module: null, isHoliday: true });
      currentDate.setDate(currentDate.getDate() + 1); // Move to next day
      day++;
      continue;
    }
    // Get all schedule templates for the current day number (templateIdx + 1)
    const dayTemplates = getDayTemplates(scheduleTemplates, templateIdx + 1);
    if (dayTemplates.length === 0) {
      // If no templates for this day, skip to next day
      currentDate.setDate(currentDate.getDate() + 1);
      day++;
      templateIdx++;
      continue;
    }
    // For each template (groupType/module) for this day, add a schedule entry
    for (const t of dayTemplates) {
      schedule.push({
        day,
        date: dateStr,
        groupType: t.groupType,
        module: t.module.name,
        moduleId: t.module.id,
        isHoliday: false,
      });
    }
    currentDate.setDate(currentDate.getDate() + 1); // Move to next day
    day++;
    templateIdx++;
  }
  // Return the full flat schedule array
  return schedule;
}

/**
 * Generates trainer assignments for each batch and module, ensuring trainers are qualified, available, and not double-booked.
 *
 * Assignment Rules:
 * - For each batch, and for each module (regardless of groupType or day), assign ONE trainer for all days that module appears.
 * - Trainer must be qualified for the module, available on all required days, and not already assigned elsewhere on those days.
 * - Trainers are assigned in a load-balanced way (least utilized first).
 * - If no eligible trainer is found, the assignment is flagged for manual intervention.
 *
 * @param schedule Flat schedule array (from generateSchedule), one entry per (day, date, groupType, module) or holiday
 * @param trainersRaw Array of trainers, each with their modules and availability
 * @param scheduleTemplates Array of schedule templates (with module info)
 * @param batchCount Number of batches to generate assignments for
 * @returns Array of assignment objects, one per (batch, groupType, module, day, date)
 */
export function generateAssignments(
  schedule: ScheduleEntry[],
  trainersRaw: TrainerWithModulesAndAvailability[],
  scheduleTemplates: (ScheduleTemplate & {
    module: { id: number; name: string; duration?: number };
  })[],
  batchCount: number,
) {
  // The final list of assignments to return
  const assignments: {
    batch: string; // e.g., "Batch 1"
    groupType: string; // e.g., "SA", "SM", etc.
    module: string; // Module name
    moduleId: number; // Module ID
    trainer: string | null; // Trainer name (null if unassigned)
    trainerId: number | null; // Trainer ID (null if unassigned)
    day: number; // Day number in the schedule
    date: string; // Date string (YYYY-MM-DD)
    needsManualAssignment?: boolean; // True if no eligible trainer found
  }[] = [];

  // Tracks if a trainer is already assigned on a given date (prevents double-booking across all batches)
  // Key: "trainerId|date" => true if assigned
  const trainerAssignments: Record<string, boolean> = {};

  // Tracks how many modules each trainer has been assigned (for load balancing)
  const trainerUtilization: Record<number, number> = {};
  trainersRaw.forEach((t) => {
    trainerUtilization[t.id] = 0;
  });

  // Loop through each batch (e.g., Batch 1, Batch 2, ...)
  for (let batchIdx = 0; batchIdx < batchCount; batchIdx++) {
    const batchLabel = `Batch ${batchIdx + 1}`;

    // Group all schedule entries by moduleId (ignoring groupType)
    // Each group contains all days/dates that a module appears in the schedule
    const groupMap = new Map<number, ScheduleEntry[]>();
    for (const sched of schedule) {
      if (sched.isHoliday || !sched.moduleId) continue; // Skip holidays and entries without a module
      if (!groupMap.has(sched.moduleId)) groupMap.set(sched.moduleId, []);
      groupMap.get(sched.moduleId)!.push(sched);
    }

    // For each module group, assign a trainer for all days that module appears
    for (const schedGroup of groupMap.values()) {
      const { module: moduleName, moduleId } = schedGroup[0];
      const allDates = schedGroup.map((s) => s.date); // All dates this module appears

      // Find the module object in the schedule templates
      const mod = moduleId ? scheduleTemplates.find((t) => t.module.id === moduleId)?.module : null;
      if (!mod) {
        // If module not found, mark all as needsManualAssignment
        for (const sched of schedGroup) {
          assignments.push({
            batch: batchLabel,
            groupType: sched.groupType!,
            module: moduleName!,
            moduleId: moduleId!,
            trainer: null,
            trainerId: null,
            day: sched.day,
            date: sched.date,
            needsManualAssignment: true,
          });
        }
        continue;
      }

      // Find trainers who:
      // - Are qualified for the module
      // - Are available on ALL required dates
      // - Are NOT already assigned (double-booked) on any of those dates
      const eligibleTrainers = trainersRaw.filter((trainer) => {
        // Must be qualified for the module
        if (!trainer.trainerModules.some((tm) => tm.module.id === mod.id)) return false;
        // Must be available and not double-booked for all dates
        for (const date of allDates) {
          // Trainer is unavailable only if there is an entry for the date with isAvailable: false
          const availabilityEntry = trainer.availability.find(
            (a) => a.availableDate.toISOString().slice(0, 10) === date,
          );
          if (availabilityEntry && availabilityEntry.isAvailable === false) return false;
          if (trainerAssignments[`${trainer.id}|${date}`]) return false; // Already assigned elsewhere
        }
        return true;
      });

      // Sort eligible trainers by utilization (least utilized first)
      sortTrainersByUtilization(eligibleTrainers, trainerUtilization);
      const assignedTrainer = eligibleTrainers[0]; // Pick the least utilized trainer

      if (assignedTrainer) {
        // Mark trainer as assigned for all relevant dates (prevents double-booking)
        for (const date of allDates) {
          trainerAssignments[`${assignedTrainer.id}|${date}`] = true;
        }
        // Increment trainer's utilization count
        trainerUtilization[assignedTrainer.id]++;
        // Add an assignment entry for each day/groupType/module
        for (const sched of schedGroup) {
          assignments.push({
            batch: batchLabel,
            groupType: sched.groupType!,
            moduleId: moduleId!,
            module: moduleName!,
            trainer: assignedTrainer.user.name,
            trainerId: assignedTrainer.id,
            day: sched.day,
            date: sched.date,
          });
        }
      } else {
        // No eligible trainer for all days, mark all as needsManualAssignment
        for (const sched of schedGroup) {
          assignments.push({
            batch: batchLabel,
            groupType: sched.groupType!,
            module: moduleName!,
            moduleId: moduleId!,
            trainer: null,
            trainerId: null,
            day: sched.day,
            date: sched.date,
            needsManualAssignment: true,
          });
        }
      }
    }
  }
  // Return the full list of assignment objects for all batches
  return assignments;
}

/**
 * Fetches all data and generates schedule preview and assignments for the batch schedule preview API.
 * Moves all ORM logic from the API route into this service function.
 */
export async function getBatchSchedulePreview({
  batchType,
  startDay,
  location,
  batchCount,
}: {
  batchType?: BatchType;
  startDay?: string;
  location?: string;
  batchCount?: number;
}) {
  const prisma = new PrismaClient();
  try {
    // Fetch schedule templates for the batchType
    const scheduleTemplates = await prisma.scheduleTemplate.findMany({
      where: batchType ? { batchType } : {},
      include: { module: true },
      orderBy: { dayNumber: 'asc' },
    });

    // Fetch holidays for the location and 30 days from startDay
    const startDate = startDay ? new Date(startDay) : new Date();
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 30);
    const locationObj = location
      ? await prisma.location.findUnique({ where: { name: location } })
      : null;
    const holidays = locationObj
      ? await prisma.holiday.findMany({
          where: {
            locationId: locationObj.id,
            date: { gte: startDate, lte: endDate },
          },
        })
      : [];

    // Collect unique module IDs required for this batch
    const requiredModuleIds = Array.from(new Set(scheduleTemplates.map((t) => t.moduleId)));

    // Fetch only trainers qualified for at least one required module, with only needed fields
    const trainersRaw = await prisma.trainer.findMany({
      where: {
        trainerModules: {
          some: {
            moduleId: { in: requiredModuleIds },
          },
        },
      },
      select: {
        id: true,
        user: { select: { name: true, email: true } },
        trainerModules: {
          select: {
            module: { select: { id: true, name: true, duration: true } },
          },
        },
        availability: {
          select: {
            availableDate: true,
            isAvailable: true,
          },
        },
      },
    });

    // Map trainers to only include id, name, email
    const trainers = trainersRaw.map((trainer) => ({
      id: trainer.id,
      name: trainer.user.name,
      email: trainer.user.email,
    }));

    // Generate schedule using service
    const schedule = generateSchedule(scheduleTemplates, holidays, startDate);

    // Generate assignments using service
    const assignments = generateAssignments(
      schedule,
      trainersRaw,
      scheduleTemplates,
      batchCount || 1,
    );

    return {
      schedule,
      holidays,
      trainers,
      assignments,
      message:
        'Schedule and assignments generated for all batches. TODO: Use real utilization counts.',
    };
  } finally {
    await prisma.$disconnect();
  }
}

export async function createRoasterWithBatches({ roasterName, createdBy, batches }: {
  roasterName: string;
  createdBy?: number;
  batches: Array<{
    batchName: string;
    batchType: string;
    batchDate: string;
    locationId: number;
    isLabBased?: boolean;
    remarks?: string;
    modules: Array<{
      moduleId: number;
      trainerId: number | null;
      groupType: string;
      date: string;
    }>;
  }>;
}) {
  const prisma = new PrismaClient();
  try {
    let roaster: Roaster | undefined;
    const createdBatches: Batch[] = [];
    const createdBatchModules: BatchModule[] = [];
    await prisma.$transaction(async (tx) => {
      // 1. Create Roaster
      roaster = await tx.roaster.create({
        data: {
          name: roasterName,
          createdBy,
        },
      });
      // 2. Create Batches and BatchModules
      for (const batch of batches) {
        const createdBatch = await tx.batch.create({
          data: {
            batchName: batch.batchName,
            batchType: batch.batchType as $Enums.BatchType,
            batchDate: new Date(batch.batchDate),
            locationId: batch.locationId,
            isLabBased: batch.isLabBased ?? false,
            remarks: batch.remarks,
            roasterId: roaster.id,
          },
        });
        createdBatches.push(createdBatch);
        for (const mod of batch.modules) {
          if (mod.trainerId === null) continue;
          const createdBatchModule = await tx.batchModule.create({
            data: {
              batchId: createdBatch.id,
              moduleId: mod.moduleId,
              trainerId: mod.trainerId,
              locationId: batch.locationId,
              date: new Date(mod.date),
              groupType: mod.groupType as $Enums.GroupType,
            },
          });
          createdBatchModules.push(createdBatchModule);
        }
      }
    });
    if (!roaster) throw new Error('Roaster was not created');
    return {
      roaster,
      batches: createdBatches,
      batchModules: createdBatchModules,
    };
  } catch (error) {
    console.error('Error in createRoasterWithBatches:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
} 
