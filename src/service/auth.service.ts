import { api } from '@/lib/utils/fetch';
import { loginResponse, loginType } from '@/types/login.type';
import { LogoutResponse } from '@/types/logout.type';
import { AuthMe } from '@/types/user.type';

export const loginService = (body: loginType) => api.post<loginResponse>('/login', body);

export const authMeService = () => api.get<AuthMe>('/me');

export const logoutService = () => api.post<LogoutResponse>('/logout');
