# Project Config (Long-Term Memory)

## Project Goals
- Build a platform for managing hiring academy operations, including candidates, trainers, batches, assessments, and onboarding.
- Provide RESTful API endpoints for CRUD operations on all core entities.
- Implement permission-based access control for different user roles (admin, trainer, candidate, etc.).
- Allow admins to manage all types of permissions and assign roles to users.
- Ensure secure, validated, and auditable data flows for all user and admin actions.
- Support scalable deployment and easy local development.

## Main Technologies
- Next.js (App Router, API routes)
- Prisma (ORM for PostgreSQL)
- TypeScript (type safety)
- Tailwind CSS (styling)
- Docker (optional, for deployment)
- ESLint & Prettier (code quality)

## Key Coding Patterns & Conventions
- API routes handle HTTP logic; business logic and validation in /lib/services and /lib/schemas.
- All incoming requests validated using Zod schemas.
- Centralized error handling for API responses.
- Use ES2021+ features and modular code structure.
- Logging for all API requests and errors.
- Role-based access control enforced at the API and UI layers.

## Constraints
- Never commit secrets or .env files.
- Only valid data per schema allowed; all input validated.
- File uploads (if any) must respect size and type constraints.
- Use Prettier and ESLint for code style.

## Tokenization Settings
- Assume 4 characters per token for estimation.
